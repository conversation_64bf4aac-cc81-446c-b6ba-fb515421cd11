<div class="main hero-unit white-text">
	<form id="promoForm" class="promocode-form">
		<fieldset>
			<legend class="white-text"><%= __("Redeem Promo Code") %></legend>
			<input type="text" placeholder="<%= __("Enter promo code") %>" name="promocode" id="promocode" style="margin-top: 20px;" size="200">
			<div id="alert">
				<div class="alert alert-succes fade in" style="display: none;" id="promocode_success">
					<strong><%= __("Promo code activated successfully!") %></strong>
				</div>
				<div class="alert alert-error fade in" style="display: none;" id="promocode_error">
					<strong></strong>
				</div>
			</div>
			<span class="help-block text-light"><%= __("Promo code must be entered with the signs of separation.") %></span>
			<button class="btn btn-large btn-info" id="promocode_activate"><%= __("Activate") %></button>
		</fieldset>
	</form>
</div>
<%_ if (promoCodesActivated.length > 0) { _%>
<div class="main hero-unit white-text">
	<h2><%= __("Activated Promo Codes") %></h2>
	<table class="table promocode-list" style="margin-bottom: 0;">
		<%_ promoCodesActivated.forEach(promoCodeActivated => { _%>
		<tr>
			<td style="width: 1%; white-space: nowrap; text-align: center;">
				<span class="promocode"><%= promoCodeActivated.get("info").get("promoCode") %></span><br>
				<span class="date"><%= moment.utc(promoCodeActivated.get("createdAt")).utcOffset(-tzOffset).format("DD.MM.YYYY HH:mm:ss") %></span> 
			</td>
			<td>
				<%= promoCodeActivated.get("strings")[0]?.get("description") || "Activated promocode." %>
			</td>
		</tr>
		<%_ }) _%>
	</table>
</div>
<%_ } _%>
<script type="text/javascript">
	function requestPromoCodeStatus(promoCodeId) {
		var requestStatusTimeout = null;
		var requestStatusInterval = setInterval(shopPromoCodeStatusAction, 1000, promoCodeId, function(result) {
			$('#promocode_success').hide();
			$('#promocode_error').hide();
			if (result.ReturnCode == 0) {
				if (result.Status == "activated") {
					clearTimeout(requestStatusTimeout);
					loadPromoCodes(function() {
						$('#promocode_success').show();
					});
				} else {
					return;
				}
			} else {
				$('#promocode_error').show();
				$('#promocode_error strong').show().append("<%= __('Activation error') %>: #" + result.ReturnCode);
			}
			clearInterval(requestStatusInterval);
		});
		requestStatusTimeout = setTimeout(function() {
			clearInterval(requestStatusInterval);
			$('#promocode_error').show();
			$('#promocode_error strong').show().append('<%= __('Activation error') %>: #0');
		}, 5000);
	}
	function activatePromocode(promocode) {
		$('#promocode_success').hide();
		$('#promocode_error').hide();
		if (promocode == "") {
			$('#promocode_error').show();
			$('#promocode_error strong').html("<%= __('Please enter your promo code.') %>");
			return;
		}
		$('#promocode_activate').prop('disabled', true);
		$('#promocode_error').hide();
		shopPromoCodeAction(promocode, function(result) {
			if (result.ReturnCode == 0) {
				requestPromoCodeStatus(result.PromoCodeId);
			} else {
				$('#promocode_error').show();
				$('#promocode_activate').prop('disabled', false);
				switch(result.ReturnCode) {
					case 9:
						$('#promocode_error strong').html("<%= __('Too many requests.') %>");
						break;
					case 1000:
						$('#promocode_error strong').html("<%= __('The specified promo code does not exist.') %>");
						break;
					case 1001:
						$('#promocode_error strong').html("<%= __('The specified promo code has expired.') %>");
						break;
					case 1002:
						$('#promocode_error strong').html("<%= __('The specified promo code has reached its activation limit.') %>");
						break;
					case 1010:
						$('#promocode_error strong').html("<%= __('The specified promo code has already been activated.') %>");
						break;
					default:
						$('#promocode_error strong').html("<%= __('Activation error') %>: #" + result.ReturnCode);
				}
			}
		});
	}

	$(function() {
		$('#promocode').change(function() {
			$('#promocode_success').hide();
			$('#promocode_error').hide();
		});
		$('#promocode_activate').click(function(e) {
			e.preventDefault();
			activatePromocode($('#promocode').val())
		});
	});
</script>
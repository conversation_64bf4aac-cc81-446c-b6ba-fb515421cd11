{"err_1044481": "Ошибка соединения с сервером STEER.", "err_1044482": "Не удается зарегистрировать сервер STEER.", "err_1044483": "STEER сервер не зарегистрирован.", "err_1044484": "Ошибка чтения данных сервера STEER.", "err_1044485": "Ошибка записи данных сервера STEER.", "err_16778215": "Сессия STEER истекла. Пожалуйста, войдите снова.", "err_16778216": "Не удается подключиться к системе STEER. Проверьте состояние сервера.", "err_16778217": "Во время связи возникло препятствие. Пожалуйста, проверьте состояние сервера.", "err_16778218": "У вас нет прав доступа. Обратитесь к администратору.", "err_16778219": "Неизвестная ошибка. Обратитесь к администратору.", "err_16778220": "У вас нет полномочий. Обратитесь к администратору.", "err_16778221": "Неверные данные локализации. Обратитесь к администратору.", "err_16778222": "Неверные данные. Обратитесь к администратору.", "err_16778223": "Произошла исключительная ситуация. Пожалуйста, свяжитесь с администратором.", "err_33554436": "Сервер не подключен.", "err_33554437": "Превышен лимит времени передачи данных", "err_83885830": "Произошла ошибка базы данных.", "err_83886080": "Обработано нормально.", "err_83886180": "Идентификатор или пароль не совпадают.", "err_83886280": "У вас нет полномочий или функция не настроена для типа выполнения", "err_83886281": "Группа пользователей инертна", "err_83886282": "Группа функций инертна", "err_83886283": "Функция инертна", "err_83886284": "Целевая группа не имеет полномочий для выполнения функции или имеет проблемы с функцией.", "err_83886285": "Тип вызова функции не определен", "err_83886286": "У вас нет полномочий или функция не настроена для типа приведения", "err_83886287": "Статус каста либо изменен, либо не принадлежит целевой группе каста", "err_83886288": "Статус каста либо изменен, либо каст не существует", "err_83886289": "Не принадлежит к группе проверки", "err_83886290": "Целевые данные не существуют", "err_83886291": "Дубликат входа в систему. Предыдущий сеанс входа завершен.", "err_83886292": "Срок действия сеанса истек.", "err_83886293": "IP-адрес клиента изменен. Пожалуйста, войдите снова.", "err_83886294": "Учетная запись уже существует.", "err_83886295": "Пользователь не может войти в систему.", "err_167771910": "Произошла ошибка базы данных.", "Invalid QA login or password.": "Неверный логин или пароль (QA).", "The session has expired.": "Сессия истекла.", "Function access denied": "Доступ к функции запрещен", "Operation failed": "Ошибка операции", "The field must contain the value as a number.": "Поле должно содержать номерное значение.", "The field contains an existing server ID.": "Поле содержит существующий ID сервера.", "The field must contain a valid IP value.": "Поле должно содержать допустимое значение IP-адреса.", "The field must contain a valid port value.": "Поле должно содержать допустимое значение порта.", "The field must be a valid value.": "Поле должно содержать допустимое значение.", "The field contains already existing language code.": "Поле содержит уже существующий код языка.", "The field must be between 1 and 256 characters.": "Поле должно содержать от 1 до 256 символов.", "The field has invalid value.": "Поле имеет недопустимое значение.", "Sync server state": "Синхронизировать состояние сервера", "Sync server state with the PlanetDB database after saving": "Синхронизировать состояние сервера с PlanetDB после сохранения", "Sync server state with the PlanetDB database is not configured.": "Синхронизация состояния сервера с PlanetDB не настроена.", "The field must be between 1 and 1024 characters.": "Поле должно содержать от 1 до 1024 символов.", "The field must be between 1 and 50 characters.": "Поле должно содержать от 1 до 50 символов.", "The field must be between 1 and 2048 characters.": "Поле должно содержать от 1 до 2048 символов.", "The field must contain a valid date.": "Поле должно содержать верную дату.", "The field contains a date that is too late.": "Поле содержит слишком позднюю дату.", "The field must be between 3 and 24 characters.": "Поле должно содержать от 3 до 24 символов.", "The field must be between 8 and 128 characters.": "Поле должно содержать от 8 до 128 символов.", "The field must contain a valid email.": "Поле должно содержать адрес электронной почты.", "The field must contain a valid number.": "Поле должно содержать номерное значение.", "Added benefit already exists.": "Добавленное преимущество уже существует.", "The field contains existing benefit ID on account.": "Поле содержит уже существующий ID на аккаунте.", "The field contains invalid function.": "Поле содержит недопустимую функцию.", "The field contains not existing promo code ID.": "Поле содержит несуществующий ID промокода.", "The field contains inactive promo code ID.": "Поле содержит неактивный ID промокода.", "The field contains expired promo code ID.": "Поле содержит просроченный ID промокода.", "The field contains the promo code ID with the activation limit reached.": "Поле содержит ID промокода с достигнутым пределом по количеству активаций.", "The field must be between 6 and 20 characters.": "Поле должно содержать от 6 до 20 символов.", "The field must be between 3 and 8 characters.": "Поле должно содержать от 3 до 8 символов.", "The field contains not existing account ID.": "Поле содержит несуществующий ID.", "The field contains an existing promo code.": "Поле содержит уже существующий промокод.", "The field contains an existing coupon.": "Поле содержит уже существующий купон.", "Shop account with specified account ID already exists.": "Аккаунт магазина с указанным ID уже существует.", "This promo code has already been activated on the specified account ID.": "Этот промокод уже активирован для указанного ID аккаунта.", "The field must be between 1 and 255 characters.": "Поле должно содержать от 1 до 255 символов.", "No items have been added to the product.": "Ни одного предмета не добавлено.", "A non-existent item has been added": "Добавлен несуществующий предмет", "Added item already exists.": "Добавленный предмет уже существует.", "The field contains already banned account ID.": "Поле содержит ID уже забаненого аккаунта.", "There are no Service Items for the specified service item IDs.": "Не существует предметов услуг для указанных ID предметов услуги.", "No items have been added to the box.": "Ни одного предмета не добавлено.", "The field contains not existing character ID.": "Поле содержит несуществующий ID.", "The field contains not existing server ID.": "Поле содержит несуществующий ID сервера.", "Task with this box ID is already running. Check tasks queue.": "Задача с данным ID коробки уже выполняется. См. очередь задач.", "The field contains already existing name.": "Поле содержит уже существующее имя.", "The field contains already existing email.": "Поле содержит уже существующий адрес электронной почты.", "The field must contain an existing category ID.": "Поле должно содержать ID существующей категории.", "Profile": "Профиль", "Logout": "Выйти", "Dashboard": "Информация", "Maintenance": "Обслуживание", "Servers": "Серверы", "Servers List (SLS)": "Список серверов (SLS)", "Strings": "Строки", "Accounts": "Аккаунты", "Benefits": "Преимущества", "Characters": "Персо<PERSON><PERSON>и", "Reports": "Отчеты", "Game Reports": "Отчеты игры", "Activity": "Активность", "Cheats": "Читы", "Chronoscrolls": "VIP-предметы", "Shop": "Мага<PERSON>ин", "Categories": "Категории", "Products": "Товары", "Shop Logs": "Журналы магазина", "Fund Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pay Logs": "Покупки", "Promo Codes": "Промокоды", "Coupons": "Купоны", "List": "Список", "Activated": "Активированные", "API Settings": "Настройки API", "Yes": "Да", "No": "Нет", "Confirm": "Принять", "unconfigured": "не настроено", "hidden": "скрыто", "optional": "не обязательно", "Confirm Deletion": "Подтвердить удаление", "Are you sure you want to delete this entries?": "Вы действительно хотите удалить элемент(ы)?", "No data available in table": "Нет данных для отображения", "Showing _START_ to _END_ of _TOTAL_ entries": "Показано с _START_ по _END_ позиций из _TOTAL_", "Showing 0 to 0 of 0 entries": "Показано с 0 до 0 позиций из 0", "(filtered from _MAX_ total entries)": "(всего отфильтровано до _MAX_ позиций)", "Show _MENU_ entries": "Показывать _MENU_ позиций", "Loading...": "Загрузка...", "Filter": "Поиск", "No matching records found": "Нет найденных соответствий", "First": "Первая", "Last": "Последняя", "Next": "Вперед", "Previous": "Назад", "activate to sort column ascending": "активировать для сортировки столбца по возрастанию", "activate to sort column descending": "активировать для сортировки столбца по убыванию", "Please enter login": "Пожалуйста, введите логин", "Please enter password": "Пожалуйста, введите пароль", "Your Profile": "Ваш профиль", "Session Information": "Информация о сеансе", "Permitted Functions": "Разрешенные функции", "User login ID": "ID пользователя", "User login SN": "SN пользователя", "Function name": "Имя функции", "Authentication type": "Тип аутентификации", "Time zone": "Временная зона", "Login ID": "ID пользователя", "Password": "Пароль", "Save account information": "Сохранить учетные данные", "Login": "Войти", "Show full report": "Показать весь отчет", "Show full log": "Показать весь журнал", "Servers Stats": "Статистика серверов", "Under maintenance": "Идет обслуживание", "Server Maintenance": "Обслуживание сервера", "Add new maintenance plan": "Добавить новый план обслуживания", "ID": "ID", "Start time": "Время начала", "End time": "Время окончания", "Description": "Описание", "Add Server Maintenance Plan": "Добавление плана обслуживания сервера", "Back to list": "Назад в список", "Add": "Добавить", "Edit Server Maintenance Plan": "Редактирование плана обслуживания сервера", "Save": "Сохранить", "Server List (SLS)": "Список серверов (SLS)", "Add new server": "Добавить новый сервер", "Login IP:port": "IP:порт авторизации", "Name": "Имя", "Language": "Язык", "Type": "Тип", "Enabled": "Разрешено", "Crowdness": "Закрыт", "Available": "Доступен", "Users online": "Польз. онлайн", "Users total": "Польз. всего", "Add Server": "Добавление сервера", "Server ID": "ID сервера", "Language code": "Код языка", "Login IP": "IP авторизации", "Login port": "Порт авторизации", "Name string": "Строка имени", "Description string": "Строка описания", "Threshold low": "Нижний предел", "Threshold medium": "Средний предел", "Only PvE": "Только PvE", "Is crowdness": "Закрыт", "Is available": "Доступен", "Is enabled": "Разрешен", "Edit Server": "Редактирование сервера", "Server List Strings": "Строки списока серверов", "Add new strings": "Добавить новые строки", "Game language": "Язык игры", "Category PvE": "Категория PvE", "Category PvP": "Категория PvP", "Server offline": "Сервер выключен", "Server low": "Низкая нагрузка", "Server medium": "Снедняя нагрузка", "Server high": "Высокая нагрузка", "Crowdness no": "Открыт", "Crowdness yes": "Закрыт", "Popup": "Высплывающее сообщение", "Add Server Strings": "Добавление строк серверов", "Edit Server Strings": "Редактирование строк серверов", "Create new account": "Создать новый аккаунт", "User name": "Имя пользователя", "Email": "Email", "Registered": "Зарегистрирован", "Permission": "Разрешения", "Privilege": "Привилегии", "Last login": "Последний вход", "Last server": "Последний сервер", "Last IP": "Последний IP", "QA": "QA", "GM": "ГМ", "MT": "ТО", "PL": "ПЛ", "Banned": "Забан<PERSON>н", "Add Account": "Добавление аккаунта", "Account Information": "Информация об аккаунте", "Account Benefits": "Преимущества аккаунта", "Benefit ID": "ID преимущества", "None": "Нет", "Available until": "Доступно до", "Add benefit": "Добавить преимущество", "Edit Account": "Редактирование аккаунта", "Ban account": "Забанить аккаунт", "Quick Actions": "Быстрое действие", "Send Box": "Отправить предмет", "Add Benefit": "Добавить преимущество", "Add Product": "Добавить товар", "Add Category": "Добавить категорию", "Start Maintenance": "Начать обслуживание", "Reset": "Сбросить", "Set GM": "<PERSON><PERSON><PERSON><PERSON> ГМ", "Set QA": "Дать QA", "Set MT": "Дать ТО", "Set PL": "Дать ПЛ", "Account Benefits List": "Список преимуществ аккаунта", "Account ID": "ID аккаунта", "Show": "Показать", "Add new benefit": "Добавить новое преимущество", "Please enter account ID.": "Пожалуйста, введите ID аккаунта.", "Benefit description": "Описание преимущества", "Add Account Benefit": "Добавление преимущества аккаунта", "Edit Account Benefit": "Редактирование преимущества аккаунта", "Male": "Мужской", "Female": "Женский", "Human": "Человек", "High Elf": "Высший эльф", "Aman": "<PERSON><PERSON><PERSON><PERSON>", "Castanic": "Кастаник", "Popori": "Попори", "Baraka": "Барака", "Elin": "<PERSON><PERSON><PERSON><PERSON>", "Warrior": "Воин", "Lancer": "Рыцарь", "Slayer": "Убийца", "Berserker": "Берсерк", "Sorcerer": "Маг", "Archer": "Луч<PERSON>и<PERSON>", "Priest": "<PERSON><PERSON><PERSON><PERSON>", "Mystic": "<PERSON>и<PERSON><PERSON><PERSON><PERSON>", "Reaper": "<PERSON><PERSON><PERSON><PERSON>", "Gunner": "Ин<PERSON><PERSON><PERSON><PERSON><PERSON>", "Brawler": "Кру<PERSON>итель", "Ninja": "Шин<PERSON><PERSON>и", "Valkyrie": "Валькирия", "Common": "Обычное", "Uncommon": "Необычное", "Rare": "Редкое", "Superior": "Исключительное", "Mythic": "Мифическое", "Account Characters List": "Список персонажей аккаунта", "Please enter account ID and select the server.": "Пожалуйста, введите ID аккаунта и выберите сервер.", "Level": "Уровень", "Race": "Раса", "Gender": "Пол", "Class": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Activity Report": "Отчет активности", "Period from": "Период от", "to": "до", "All": "Все", "Report time": "Время записи", "Action (playtime)": "Действие (время игры)", "Client IP": "IP клиента", "Create": "Создание", "Edit": "Изменение", "Delete": "Удаление", "Characters Report": "Отчет персонажей", "Action": "Действие", "Cheats Report": "Отчет о читах", "Cheat info": "Инофрмация о чите", "Character": "Перс<PERSON><PERSON><PERSON>", "Skill": "Скилл", "Hits": "Удары", "Limit": "<PERSON>и<PERSON><PERSON><PERSON>", "Target": "Цель", "Location": "Локация", "Other": "Другое", "Chronoscrolls Report": "Отчет о VIP-предметах", "Chrono ID": "ID предмета", "Info": "Информация", "Shop Accounts": "Аккаунты магазина", "Accounts are created automatically when funds are deposited to the user's Shop balance.": "Аккаунты создаются автоматически при зачислении средств на баланс магазина пользователя.", "If additional information is not set, the information for the product will be taken from the first item added.": "Если дополнительная информация не задана, информация о товаре будет взята из первого добавленного предмета.", "In the Last login field, the date of the last entry into the game is specified. Box will not be sent to users who logged into the game before this date.": "В поле 'Последний вход' указыается дата последнего входа в игру. Коробка не будет оптравлена пользователям, которые последний раз заходили раньше этой даты.", "Balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Discount": "Скидка", "Active": "Активно", "Valid": "Доступно", "Activations": "Активации", "No change": "Не изменять", "Inactive": "Не активно", "Enter game": "Вход", "Leave game": "Выход", "count": "кол-во", "example": "пример", "Shop Categories": "Категории магазина", "Add new category": "Добавить новую категорию", "Sort": "Сортировка", "Sort.": "Сорт.", "Title": "Заголовок", "Add Shop Cetegory": "Добавление категории магазина", "Category Title": "Заголовок категории", "en": "Английский", "ru": "Русский", "cn": "Китайский", "fr": "Французский", "de": "Немецкий", "jp": "Японский", "kr": "Корейский", "se": "Шведский", "th": "Тайский", "tw": "Китайск<PERSON> (Тайвань)", "us": "Английский (США)", "Edit Shop Category": "Редактирование категории магазина", "Slides": "Слайды", "Shop Slides": "Слайды магазина", "Add new slide": "Добавить новый слайд", "Prio.": "Приор.", "Priority": "Приоритет", "Product title": "Заголовок товара", "Display start date": "Дата начала показа", "Display end date": "Дата окончания показа", "Background image": "Фоновая картинка", "Preview": "Предпросмотр", "Upload new": "Загрузить новую", "Required resolution: %sx%sx": "Необходимое разрешение: %sx%s", "not used": "не использ.", "File upload": "Загрузить файл", "Remove image": "Удалить картинку", "Background image file not selected.": "Файл фоновой картинки не выбран.", "The resolution must be: %sx%s": "Разрешение должно быть: %sx%s", "Only JPG and PNG files are allowed": "Разрешены только JPG и PNG файлы", "The file is too big! Max size: %s MB": "Файл слишком большой! Макс. размер: %s МБ", "File too large": "Файл слишком большой", "This image is used in the slide.": "Данная картинка используется слайде.", "Error deleting image.": "Ошибка удаления картинки.", "Add Shop Slide": "Добавить слайд магазина", "Edit Shop Slide": "Изменить слайд магазина", "Shop Pay Logs": "Журнал покупок в магазине", "Shop Fund Logs": "Жу<PERSON><PERSON>л изменения баланса магазина", "Log time": "Время записи", "Quantity": "Кол-во", "Amount": "Сумма", "Operation description": "Описание операции", "Product ID": "ID товара", "Box ID": "ID коробки", "Price": "Цена", "Tag": "Ярлык", "Status": "Статус", "Updated": "Обновлен", "Shop Promo Codes": "Промокоды магазина", "Create new promo code": "Создать новый промокод", "Promo code": "Промокод", "Assigned function": "Назначенная функция", "Valid from": "Доступно от", "Valid to": "Доступно до", "Tag valid from": "Ярлык действителен с", "Tag valid to": "Ярлык действителен до", "Discount valid from": "Скидка доступна с", "Discount valid to": "Скидка доступна до", "Maximum of activations": "Максимум активаций", "no limit on the number of activations.": "без ограничений на число активаций.", "Add Shop Promo Code": "Добавление промокода", "Promo Code Information": "Информация о промокоде", "Promo Code Description": "Описание промокода", "Edit Shop Promo Code": "Редактирование промокода", "Shop Activated Promo Codes": "Активированные промокоды магазина", "Promo code ID": "ID промокода", "Activate promo code": "Активировать промокод", "Please enter account ID or promo code ID.": "Пожалуйста, введите ID аккаунта или ID промокода.", "Activation time": "Время активации", "Activate Promo Code": "Активация промокода", "Activate": "Активир<PERSON>ать", "Shop Coupons": "Купоны магазина", "Add Shop Coupon": "Добавление купон", "Edit Shop Coupon": "Редактирование купона", "Coupon Information": "Информация о купоне", "Shop Activated Coupons": "Активированные купоны магазина", "Create new coupon": "Создать новый купон", "Coupon": "Купон", "Coupon ID": "ID купона", "Please enter account ID or coupon ID.": "Пожалуйста, введите ID аккаунта или ID купона.", "Configuration Variables": "Параметры конфигурации", "Parameter": "Параметр", "Value": "Значение", "Add Shop Account": "Добавление аккаунта магазина", "Edit Shop Account": "Редактирование аккаунта магазина", "Shop Products": "Товары магазина", "Category": "Категория", "Category ID": "ID категории", "Items count": "Кол-во предметов", "Icon": "Иконка", "Published": "Размещено", "Add new product": "Добавить новый товар", "Add Shop Product": "Добавление нового товара", "Edit Shop Product": "Редактирование товара", "Product Information": "Инрформация о товаре", "Product Items": "Предметы товара", "Add item": "Добавить предмет", "Tag Information": "Информация о ярлыке", "Discount Information": "Информация о скидке", "Additional Information": "Дополнительная информация", "Rare grade": "Качество", "Item count": "Кол-во предметов", "Item ID": "ID предмета", "Item": "Предмет", "Service item ID": "ID предмета услуги", "Count": "Кол-во", "Validate form": "Проверить форму", "Item template ID": "ID шаблона предмета", "Registered Users": "Зарегистрировано", "Online Users": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "completed": "Завершено", "deposit": "Начато", "rejected": "Отклонено", "PromoCode": "Промокод", "ChronoScroll": "VIP-предмет", "ShopApi": "API магазина", "SignUp": "Регистрация", "AccountDeletion": "Удаление аккаунта", "BalanceChange": "Изменение баланса", "Buy": "Покупка", "BuyCancel": "Отмена покупки", "Bans": "Баны", "Account Bans": "Баны аккаунтов", "Edit Ban Account": "Редактировать бан аккаунта", "Ban Account": "Забанить аккаунт", "Kick all online players": "Отключить всех игроков", "Operations": "Операции", "Gateway API": "Gateway API", "Admin Operations Report": "<PERSON><PERSON><PERSON><PERSON>л операций администратора", "Gateway API Report": "Журнал Gateway API", "Function": "Функция", "Endpoint": "Эндпоинт", "IP address": "IP адрес", "View Operations Report": "Просмотр журнала операций", "View Gateway API Report": "Просмотр журнала Gateway API", "Parameters": "Параметры", "Operation Parameters": "Параметры операции", "Request Parameters": "Параметры запроса", "Item Claim": "Отправка предметов", "Send": "Отправить", "Send Online": "Отправить онлайн", "Send All": "Отправить всем", "Tasks Queue": "Очередь задач", "Queue": "Очередь", "Items (Box)": "Предметы", "Item Claim Boxes": "Отправка предметов", "Add new box": "Добавить новую коробку", "Days": "Число дней", "Add Item Claim Box": "Добавление коробки", "Box Information": "Информация о коробке", "Box Items": "Предметы в коробке", "Edit Item Claim Box": "Редактирование коробки", "Items": "Предметы", "Admin Tasks Queue": "Очередь задач администрирования", "Admin Tasks Queue Log": "<PERSON><PERSON><PERSON><PERSON>л очереди задач администрирования", "Restart tasks queue": "Перезапустить очередь", "Cancel all tasks": "Отменить все задачи", "Cancel failed tasks": "Отменить неудачные задачи", "Show tasks log": "Показа<PERSON>ь журнал задач", "Message": "Сообщение", "Created": "Создано", "Pending": "Ожидание", "Failed": "Неудача", "Cancelled": "Отменено", "Success": "Успешно", "Time": "Время", "Task ID": "ID задачи", "Task name": "Имя задачи", "Task tag": "Тег задачи", "Send Item Claim Box": "Отправка коробки", "Recipient Information": "Информация о получателе", "Character ID": "ID персонажа", "Send Item Claim Box (All Users)": "Отправка коробки (всем пользователям)", "Results of Sending Item Claim Box": "Результаты отправки коробки", "Show tasks": "Просмотр задач", "Operation has errors during execution": "Операция имеет ошибки в ходе выполнения", "Operation completed successfully.": "Операция выполнена успешно.", "Operation is running in the background...": "Операция выполняется в фоновом режиме...", "Rejected with message": "Отменено с сообщением", "Boxes": "Коробки", "Log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Item Claim Boxes Logs": "<PERSON><PERSON><PERSON><PERSON>л отправки предметов", "Show all tasks": "Показать все задачи", "Log ID": "ID записи", "Box": "Коробка", "Chronoscroll": "VIP-предмет", "[unknown]": "[неизвестно]", "With selected": "С отмеченными", "d.": "д.", "hr.": "ч.", "min.": "мин.", "sec.": "сек.", "Change password": "Изменить пароль", "The API settings contain a dynamic overriding of the server information.": "Настройки API содержат динамическое переопределение информации о сервере.", "Launcher": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "Launcher Logs": "<PERSON><PERSON><PERSON><PERSON><PERSON> лаунчера", "Version": "Версия", "Crash game": "Сбой клиента", "Exit game": "Выход", "Sign in": "Авторизация", "Start download": "Начало скачивания", "Finish download": "Окончание скачивания", "Downloading": "Скачивание", "Versions Information": "Информация о версиях", "Node.js Version": "Версия Node.js", "TERA API Version": "Версия TERA API", "DB Version": "Версия БД"}
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Server Strings") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="language"><%= __("Language code") %></label>
								<select class="form-control boxed" name="language" disabled>
									<%_ availableLanguages.forEach(languageCode => { _%>
									<option value="<%= languageCode %>" <%= languageCode === language ? "selected" : "" %>>(<%= languageCode %>) <%= __(languageCode) %></option>
									<%_ }); _%>
								</select>
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="categoryPvE"><%= __("Category PvE") %></label>
								<input type="text" class="form-control boxed" name="categoryPvE" value="<%= categoryPvE %>" minlength="1" maxlength="50">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="categoryPvP"><%= __("Category PvP") %></label>
								<input type="text" class="form-control boxed" name="categoryPvP" value="<%= categoryPvP %>" minlength="1" maxlength="50">
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="serverOffline"><%= __("Server offline") %></label>
								<input type="text" class="form-control boxed" name="serverOffline" value="<%= serverOffline %>" minlength="1" maxlength="50">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="serverLow"><%= __("Server low") %></label>
								<input type="text" class="form-control boxed" name="serverLow" value="<%= serverLow %>" minlength="1" maxlength="50">
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="serverMedium"><%= __("Server medium") %></label>
								<input type="text" class="form-control boxed" name="serverMedium" value="<%= serverMedium %>" minlength="1" maxlength="50">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="serverHigh"><%= __("Server high") %></label>
								<input type="text" class="form-control boxed" name="serverHigh" value="<%= serverHigh %>" minlength="1" maxlength="50">
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="crowdNo"><%= __("Crowdness no") %></label>
								<input type="text" class="form-control boxed" name="crowdNo" value="<%= crowdNo %>" minlength="1" maxlength="50">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="crowdYes"><%= __("Crowdness yes") %></label>
								<input type="text" class="form-control boxed" name="crowdYes" value="<%= crowdYes %>" minlength="1" maxlength="50">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label" for="popup"><%= __("Popup") %></label>
							<textarea class="form-control boxed" name="popup" minlength="1" maxlength="2048"><%= popup %></textarea>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
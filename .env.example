##################################
# LOGGING CONFIGURATION
##################################

# Set log level (available levels: debug, info, warn, error).
LOG_LEVEL=debug

# Enable a write log to file.
LOG_WRITE=true

# Set the directory for write a log files (if leave empty, "logs" is used).
LOG_WRITE_DIRECTORY=

# Enable API requests/responses logging.
LOG_API_REQUESTS=true

# Enable log IP addresses.
LOG_IP_ADDRESSES=false

##################################
# INTEGRATION CONFIGURATION
##################################

# Enable Steer Server session (used for Admin Panel authorization).
# For authorization with the Steer server to work, you need to 
# import the "share/steer/ExportSteerData_steeradmin_API.sef" file in the
# Steer web panel, create a user and give permissions.
STEER_ENABLE=false

# Set a host and port to connect to the Steer Server Hub.
STEER_HOST=127.0.0.1
STEER_PORT=8105

# Set a host and port to connect to the Platform Hub for send requests when performing
# certain actions, such as: server test, instant benefit activation, sending premium items, etc.
HUB_HOST=127.0.0.1
HUB_PORT=11001

##################################
# ARBITER API CONFIGURATION
##################################

# Set the host for receiving connections from the Arbiter server (TERA Server).
# Use 0.0.0.0 or leave empty for bind API on all IPs (not recomended).
API_ARBITER_LISTEN_HOST=127.0.0.1

# Set the port for receiving connections from the Arbiter server.
API_ARBITER_LISTEN_PORT=8080

# Ebable reporting game activity (login and logout) to the reports database.
API_ARBITER_REPORT_ACTIVITY=true

# Enable reporting characters information (create, modify, delete) to the reports database.
API_ARBITER_REPORT_CHARACTERS=true

# Enable reporting using of chronos crolls (premium/vip/tera-club items) to the reports database.
API_ARBITER_REPORT_CHRONOSCROLLS=true

# Enable reporting cheats information to the reports database.
API_ARBITER_REPORT_CHEATS=true

# Enable Arbiter server availability check based on TCP port check.
# If such a check is disabled, the Arbiter server availability is checked only by
# requesting server status from the Platform Hub.
API_ARBITER_SERVER_PORT_CHECK=true

# Use client IP address from Launcher instead of IP address from Arbiter server.
# This may need to be enabled when using Tera Server Proxy.
# If LOG_IP_ADDRESSES_FORWARDED_FOR=true, the IP from the X-Forwarded-For header will be used.
API_ARBITER_USE_IP_FROM_LAUNCHER=false

##################################
# PORTAL API CONFIGURATION
##################################

# Set the host for receiving connections from the users for access to Portal or Launcher.
# Use 0.0.0.0 or leave empty for bind API on all IPs.
API_PORTAL_LISTEN_HOST=

# Set the port for receiving connections from the users for access to Portal or Launcher.
API_PORTAL_LISTEN_PORT=80

# Allow determination of client IP address based on "X-Forwarded-For" header.
# This must be enabled if a reverse proxy is used. It is also necessary to specify the reverse
# proxy IP address in parameter API_PORTAL_TRUSTPROXY_HOSTS, otherwise data spoofing is possible.
API_PORTAL_TRUSTPROXY_ENABLE=false

# List of IP addresses or subnets that should be trusted as a reverse proxy.
# Multiple entries can be listed separated by commas.
# If left empty, headers will be accepted from any IP address (not recommended!).
API_PORTAL_TRUSTPROXY_HOSTS=

# Enable caching of Portal and Launcher web server content.
API_PORTAL_ENABLE_CACHE=true

# Set language for the Portal or Launcher web interface (available locales: en, ru).
API_PORTAL_LOCALE=en

# Enable language selector in the Launcher web interface.
API_PORTAL_LOCALE_SELECTOR=true

# CHANGE IT!
# Specify the secret word required to encrypt sessions.
API_PORTAL_SECRET=your_secret_word

# Set the project brand name.
API_PORTAL_BRAND_NAME=Tera Private

# Enable In-game Shop features.
# Setup shop on game server:
# 1) Remove <Shop url="..." /> from "DeploymentConfig.xml".
# 2) Set the endpoint in "config_arb_gw.txt" as:
#    web_shop_url=http://YOUR_API_PORTAL_HOST/tera/ShopAuth?authKey=%s
API_PORTAL_SHOP_ENABLE=true

# Set the initial balance value of In-game Shop account on user registration.
# Set 0 as value or remove/comment parameter to disable this.
API_PORTAL_SHOP_INITIAL_BALANCE=0

# Set the ID of the benefit that will be activated for the VIP/premium status.
# See StrSheet_AccountBenefit for more details about available IDs.
# If leave empty, the ID 433 (TERA Club) will be used.
API_PORTAL_BENEFIT_ID_ELITE_STATUS=433

# Set the initial benefits that will be assigned to the user on registration.
# The value indicates the number of days the benefit is valid.
# Set 0 as value or remove/comment parameter to disable the benefit.
API_PORTAL_INITIAL_BENEFIT_ID_433_DAYS=30 # TERA Club
API_PORTAL_INITIAL_BENEFIT_ID_434_DAYS=3600 # Veteran

# Enable public directory for static files (needed for Launcher static files).
# Also, this directory can be used to place Launcher and Client update files.
API_PORTAL_PUBLIC_FOLDER_ENABLE=true

# Enable a CAPTCHA for validation during registration.
API_PORTAL_CAPTCHA_ENABLE=true

# Set the privilege number that has access to Launcher QA functions.
API_PORTAL_LAUNCHER_QA_PRIVILEGE=10

# Set the default region name for Client started with Launcher (e.g. EUR, RUS, TW).
API_PORTAL_CLIENT_DEFAULT_REGION=EUR

# Set a list of supported regions by Client with language names.
API_PORTAL_CLIENT_REGIONS_EUR=English
API_PORTAL_CLIENT_REGIONS_RUS=Русский
API_PORTAL_CLIENT_REGIONS_TW=中国人

# Set the URL address for placing Client and Launcher updates files.
# If leave it empty, the "/public/patch" root will be used.
API_PORTAL_CLIENT_PATCH_URL=

# Disable automatic client enter to the game server.
API_PORTAL_DISABLE_CLIENT_AUTO_ENTER=false

# Disable launcher and launcher API (BHS).
API_PORTAL_LAUNCHER_DISABLE=false

# Disable built-in register feature in the launcher.
API_PORTAL_LAUNCHER_DISABLE_REGISTRATION=false

# Enable built-in email confirmation and password reset functions.
API_PORTAL_LAUNCHER_ENABLE_EMAIL_VERIFY=false

# Disable checking of client files when the game start.
# This does not affect the process of receiving client updates.
API_PORTAL_LAUNCHER_DISABLE_CONSISTENCY_CHECK=false

# Enable the client file check disallow.
# If set to true, the launcher will not check for client updates.
API_PORTAL_CLIENT_PATCH_NO_CHECK=true

# Enable sha512 password hashing when storing in database.
API_PORTAL_USE_SHA512_PASSWORDS=false

# Set the salt to calculate the sha512 hash (not used by default).
API_PORTAL_USE_SHA512_PASSWORDS_SALT=

# Outgoing mail sender name.
API_PORTAL_EMAIL_FROM_NAME=Server Administrator

# Outgoing mail sender email address.
API_PORTAL_EMAIL_FROM_ADDRESS=<EMAIL>

##################################
# GATEWAY API CONFIGURATION
##################################

# Set the host for receiving connections from the external website (like billing).
# Use 0.0.0.0 or leave empty for bind API on all IPs (not recomended).
API_GATEWAY_LISTEN_HOST=127.0.0.1

# Set the port for receiving connections from the external website.
API_GATEWAY_LISTEN_PORT=8040

##################################
# ADMIN PANEL CONFIGURATION
##################################

# Set the host for receiving connections for access to Admin Panel.
# Use 0.0.0.0 or leave empty for bind API on all IPs.
ADMIN_PANEL_LISTEN_HOST=

# Set the port for receiving connections for access to Admin Panel.
ADMIN_PANEL_LISTEN_PORT=8050

# Allow determination of client IP address based on "X-Forwarded-For" header.
# This must be enabled if a reverse proxy is used. It is also necessary to specify the reverse
# proxy IP address in parameter API_PORTAL_TRUSTPROXY_HOSTS, otherwise data spoofing is possible.
ADMIN_PANEL_TRUSTPROXY_ENABLE=false

# List of IP addresses or subnets that should be trusted as a reverse proxy.
# Multiple entries can be listed separated by commas.
# If left empty, headers will be accepted from any IP address (not recommended!).
ADMIN_PANEL_TRUSTPROXY_HOSTS=

# Set language for the Admin Panel (available locales: en, ru).
ADMIN_PANEL_LOCALE=en

# CHANGE IT!
# Specify the secret word required to encrypt authorization sessions.
ADMIN_PANEL_SECRET=your_secret_word

# Set login for QA administrator (only for Admin Panel tests).
# QA administrator account only available if option "STEER_ENABLE" is set to "false".
ADMIN_PANEL_QA_USER=apiadmin

# Set password for QA administrator.
ADMIN_PANEL_QA_PASSWORD=password

##################################
# MAILER CONFIGURATION
##################################

# Set the host to connect to the SMTP server.
MAILER_SMTP_HOST=

# Set the port to connect to the SMTP server.
MAILER_SMTP_PORT=587

# Enabling encryption of connections to the SMTP server.
MAILER_SMTP_SECURE=false

# Account name for authorization on the SMTP server.
MAILER_SMTP_AUTH_USER=

# Account password for authorization on the SMTP server.
MAILER_SMTP_AUTH_PASSWORD=

##################################
# DATASHEET CONFIGURATION
##################################

# Enable binary DataCenter mode.
# If true, the "DataCenter_Final_<region>.dat" file from the "data/datasheets"
# directory will be used (where <region>: "EUR", "RUS", etc.).
# If false, the unpacked XML files from the "data/datasheets/<locale>"
# directory will be used (where <locale>: "en", "ru", etc.).
DATASHEET_USE_BINARY=true

# Enable if "DataCenter_Final_<region>.dat" files are compressed.
DATASHEET_DATACENTER_IS_COMPRESSED=true

# Enable if 64-bit version of "DataCenter_Final_<region>.dat" files is used.
DATASHEET_DATACENTER_HAS_PADDING=true

# Set Key for decrypting files "DataCenter_Final_<region>.dat".
# Leave empty if the datacenter file is not encrypted.
DATASHEET_DATACENTER_KEY=1c01c904ff76ff06c211187e197b5716

# Set IV for decrypting files "DataCenter_Final_<region>.dat".
# Leave empty if the datacenter file is not encrypted.
DATASHEET_DATACENTER_IV=396c342c52a0c12d511dd0209f90ca7d

##################################
# DATABASE CONFIGURATION (MYSQL)
##################################

# Set a host to connect to database.
DB_HOST=127.0.0.1

# Set a port to connect to database.
DB_PORT=3306

# Set the name of database.
DB_DATABASE=teraapi

# Set the user to connect to database.
DB_USERNAME=root

# Set the password to connect to database.
DB_PASSWORD=

##################################
# PLANETDB DATABASE CONFIGURATION (SQL SERVER)
#
# Game server database, used for API purposes (such as characters sync).
# The parameters are specified in the format "DB_PLANETDB_<ServerID>_",
# where <ServerID> is the identifier of the game server.
##################################

# Example configuration for server ID 2800:

# Enable connect and using the database.
DB_PLANETDB_2800_ENABLED=false

# Set a host to connect to database.
DB_PLANETDB_2800_HOST=127.0.0.1

# Set a port to connect to database.
DB_PLANETDB_2800_PORT=1433

# Set the name of database.
DB_PLANETDB_2800_DATABASE=PlanetDB_2800

# Set the user to connect to database.
DB_PLANETDB_2800_USERNAME=sa

# Set the password to connect to database.
DB_PLANETDB_2800_PASSWORD=

##################################
# IPAPI.IS CLIENT CONFIGURATION
##################################

# List of API keys to submit requests to, must be separated by commas.
# If left blank, the request will be submitted without an API key.
# To get a free API key, go here https://ipapi.is/app/signup
IPAPI_API_KEYS=

# Set the query cache lifetime (seconds).
IPAPI_CACHE_TTL=86400 # 24 hours

# Set the time to wait for a response to a request (seconds).
IPAPI_REQUEST_TIMEOUT=3

# Set the number of attempts to retry a request in case of a timeout.
IPAPI_REQUEST_MAX_RETRIES=3

var host = "http://" + location.hostname + ((location.port && location.port != 80) ? ":" + location.port : ""); // Only HTTP is supported!
var locale = navigator.language || navigator.userLanguage;
var secure = location.protocol == "https:";
var debug = DEBUG_STR;

function getUrlParam(name) {
	var results = new RegExp("[\\?&]" + name + "=([^&#]*)")
		.exec(window.location.search);
	return (results !== null) ? results[1] || "" : "";
}

function regionToLanguage(region) {
	if (typeof REGIONS[region] != "undefined") {
		return REGIONS[region];
	} else {
		return "en";
	}
}

/**
 * Init
 */
$(function() {
	document.body.onselectstart = function(event) {
		if (event.target.tagName == "INPUT" || event.target.tagName == "TEXTAREA") {
			return true;
		}

		return false;
	};

	initErrorMsgArray();
	Launcher.startup();
	
	// 确保DOM加载完成后设置拖动区域
	setTimeout(function() {
		Launcher.setupDragRegion();
	}, 200);
});

/**
 * Launcher REST API
 */
var LauncherAPI = {
	loginAction: function(login, password) {
		return LauncherAPI.request("LoginAction", {
			login: login,
			password: password
		});
	},

	logoutAction: function(authKey) {
		return LauncherAPI.request("LogoutAction", {
			authKey: authKey
		});
	},

	resetPasswordAction: function(email, hcaptchaToken) {
		var data = {
			email: email
		};
		
		// 如果有 hCaptcha token，添加到请求数据中
		if (hcaptchaToken) {
			data['h-captcha-response'] = hcaptchaToken;
		}
		
		return LauncherAPI.request("ResetPasswordAction", data);
	},

	resetPasswordVerifyAction: function(code, password) {
		var data = {
			code: code,
			password: password
		};
		
		return LauncherAPI.request("ResetPasswordVerifyAction", data);
	},

	signupAction: function(login, email, password, hcaptchaToken) {
		var data = {
			login: login,
			email: email,
			password: password
		};
		
		// 如果有 hCaptcha token，添加到请求数据中
		if (hcaptchaToken) {
			data['h-captcha-response'] = hcaptchaToken;
		}
		
		return LauncherAPI.request("SignupAction", data);
	},

	signupVerifyAction: function(code) {
		var data = {
			code: code
		};
		
		return LauncherAPI.request("SignupVerifyAction", data);
	},

	getMaintenanceStatusAction: function() {
		return LauncherAPI.request("GetMaintenanceStatusAction");
	},

	getCharacterCountAction: function() {
		return LauncherAPI.request("GetCharacterCountAction");
	},

	getAuthKeyAction: function() {
		return LauncherAPI.request("GetAuthKeyAction");
	},

	getAccountInfoAction: function() {
		return LauncherAPI.request("GetAccountInfoAction");
	},

	setAccountLanguageAction: function(language) {
		return LauncherAPI.request("SetAccountLanguageAction", {
			language: language
		});
	},

	endGameReport: function(code1, code2, version) {
		return LauncherAPI.request("ReportAction", {
			code1: code1,
			code2: code2,
			version: version
		});
	},

	actionReport: function(action, label, optLabel) {
		var data = {
			action: action
		};

		if (label) {
			data.label = label;
		}

		if (optLabel) {
			data.optLabel = optLabel;
		}

		return LauncherAPI.request("ReportAction", data);
	},

	request: function(action, params) {
		var response = null;

		$.ajax({
			url: "/launcher/" + action + "?locale=" + locale + "&secure=" + secure + "&ts=" + Date.now(),
			method: params ? "post" : "get",
			data: params,
			async: false,
			success: function(data) {
				response = data;
			}
		});

		return response;
	}
};

/**
 * Launcher UI
 */
var Launcher = {
	status: 0,
	isLoginSuccess: false,

	// 设置Tauri拖动区域的辅助函数
	setupDragRegion: function() {
		if (typeof window.__TAURI__ !== 'undefined') {
			// 设置主要容器为拖动区域
			$("body").attr("data-tauri-drag-region", "");
			$(".wrap").attr("data-tauri-drag-region", "");
			
			// 设置交互元素为非拖动区域
			$("button, a, input, textarea, select").each(function() {
				$(this).removeAttr("data-tauri-drag-region");
			});
			
			// 特别处理表单区域和其他交互元素
			$(".form-horizontal").removeAttr("data-tauri-drag-region");
			$(".form-control").removeAttr("data-tauri-drag-region");
			$(".msg-modal").removeAttr("data-tauri-drag-region");
			$(".progress").removeAttr("data-tauri-drag-region");
			$(".gamestart").removeAttr("data-tauri-drag-region");
			$(".dps-plugin").removeAttr("data-tauri-drag-region");
			$(".btn-close").removeAttr("data-tauri-drag-region");
			$(".btn-logout").removeAttr("data-tauri-drag-region");
			$("#playButton").removeAttr("data-tauri-drag-region");
			$("#dpsButton").removeAttr("data-tauri-drag-region");
			
			console.log("Tauri拖动区域设置完成");
		}
	},

	startup: function() {
		Launcher.sendCommand("client|0,80,1024,768");
		Launcher.sendCommand("loaded");

		// 设置Tauri拖动区域
		Launcher.setupDragRegion();

		$("#msg-modal").click(function(e) {
			e.stopPropagation();
		});

		$("form, .sc-placeholder-container, .msg-modal .close-btn").click(function() {
			Launcher.hideMessage();
		});

		// 关闭按钮事件处理
		$(".btn-close").click(function() {
			Launcher.sendCommand("close");
		});

		// 游戏启动按钮事件处理
		$(document).on("click", "#playButton", function() {
			if ($(this).hasClass("ready")) {
				Launcher.onButtonClick();
			}
		});
	},

	loaded: function(w, h) {
		Launcher.sendCommand("size|" + w + "," + h);
		$(".form-horizontal").show();
		
		// 在内容加载完成后重新设置拖动区域
		setTimeout(function() {
			Launcher.setupDragRegion();
		}, 100);
	},

	goTo: function(page, hide) {
		if (hide) {
			Launcher.sendCommand("size|0,0");
		}

		$(".form-horizontal").hide();

		if (page.indexOf("?") != -1) {
			location.replace(page + "&ts=" + Date.now());
		} else {
			location.replace(page + "?ts=" + Date.now());
		}
	},

	/*
	 * Command wrapper
	 */
	sendCommand: function(command) {
		// 检查是否在 Tauri 环境中
		if (typeof window.__TAURI__ !== 'undefined' && window.__TAURI__.invoke) {
			if (command.startsWith('size|')) {
				const sizepart = command.substring(5);
				const [width, height] = sizepart.split(',').map(Number);
				console.log('调整窗口大小:', width + 'x' + height);
				
				// 直接调用 Tauri 命令
				window.__TAURI__.invoke('set_window_size', { width: width, height: height })
					.then(function() {
						console.log('窗口大小调整成功');
					})
					.catch(function(error) {
						console.error('窗口大小调整失败:', error);
					});
			} else if (command === 'close') {
				console.log('退出程序');
				// 调用 Tauri 退出命令
				window.__TAURI__.invoke('exit_app')
					.then(function() {
						console.log('程序退出成功');
					})
					.catch(function(error) {
						console.error('程序退出失败:', error);
					});
			} else if (command.startsWith('execute|')) {
				const region = command.substring(8);
				window.__TAURI__.invoke('launch_game', { region: region })
					.then((result) => {
						console.log('游戏启动成功:', result);
					})
					.catch((error) => {
						Launcher.showError('游戏启动失败: ' + error);
						Launcher.enableLaunchButton("btn-gamestart");
					});
			} else if (command.startsWith('login|')) {
				const accountId = command.substring(6); // 去掉 "login|" 前缀
				window.BACKUP_ACCOUNT_ID = accountId;
			
				const authKey = l2w_getOTP();
				const rawCharCount = l2w_getUserCharCnt(); // 原始字符串，后端解析
				const characterCount = rawCharCount || "0"; // 直接传递原始字符串给后端
			
				const finalUserNo = parseInt(accountId);
				if (!finalUserNo || isNaN(finalUserNo) || !authKey || !characterCount) {
					console.error('认证信息不完整，无法设置:', { finalUserNo, authKey, characterCount });
					return;
				}
			
				const authParams = {
					userNo: finalUserNo,
					userName: accountId,
					authKey: authKey,
					characterCount: characterCount
				};
			
				window.__TAURI__.invoke('set_auth_info', { params: authParams })
					.then(() => console.log('登录认证信息设置成功'))
					.catch((error) => console.error('设置认证信息失败:', error));
			} else if (command === 'check_p') {
			// 在 Tauri 环境中，调用补丁检查命令
			console.log('=== 收到 check_p 命令，开始调用后端 ===');
			window.__TAURI__.invoke('check_patch_command')
				.then(function(result) {
					console.log('补丁检查完成:', result);
				})
				.catch(function(error) {
					console.error('补丁检查失败:', error);
					// 如果检查失败，默认启用游戏按钮
					Launcher.enableLaunchButton("btn-gamestart");
				});
			} else if (command === 'loaded') {
				// 加载完成命令
				console.log('加载完成命令');
					} else if (command.startsWith('client|')) {
			// 客户端命令
			console.log('客户端命令:', command);
		} else if (command === 'abort_p') {
			// 中断补丁处理命令
			console.log('收到中断补丁命令');
			window.__TAURI__.invoke('abort_patch_command')
				.then(function(result) {
					console.log('补丁中断完成:', result);
				})
				.catch(function(error) {
					console.error('补丁中断失败:', error);
				});
		} else if (command.startsWith('start_p|')) {
			// 开始补丁处理命令
			const action = parseInt(command.split('|')[1]);
			console.log('开始补丁处理命令，动作:', action);
			
			window.__TAURI__.invoke('start_patch_process_command', { action: action })
				.then(function(result) {
					console.log('补丁处理完成:', result);
				})
				.catch(function(error) {
					console.error('补丁处理失败:', error);
				});
		} else {
			console.log('未处理的命令:', command);
		}
		} 
	},

	/*
	 * Message modal events
	 */
	showSuccess: function(msg) {
		$("#msg-modal").removeClass("error");
		$("#msg-modal").addClass("success");
		$("#msg-modal-title").html("成功:");
		$("#msg-modal span").html(msg);
		$("#msg-modal").fadeIn(100);
		// 确保消息模态框显示后拖动区域设置正确
		setTimeout(function() {
			Launcher.setupDragRegion();
		}, 50);
	},

	showError: function(msg) {
		$("#msg-modal").removeClass("success");
		$("#msg-modal").addClass("error");
		$("#msg-modal-title").html("错误:");
		$("#msg-modal span").html(msg);
		$("#msg-modal").fadeIn(100);
		// 确保消息模态框显示后拖动区域设置正确
		setTimeout(function() {
			Launcher.setupDragRegion();
		}, 50);
	},

	hideMessage: function() {
		$("#msg-modal").fadeOut(100);
	},

	/*
	 * Reporting handlers
	 */
	gameEnd: function(code1, code2) {
		LauncherAPI.endGameReport(code1, code2, null);
	},

	logAction: function(action, label, optLabel) {
		LauncherAPI.actionReport(action, label, optLabel);
	},

	/*
	 * Login action events
	 */
	login: function() {
		console.log('Login函数被调用');
		$("#progressBar1").width(0);
		$("#progressBar2").width(0);
		$("#fileName").text("");
		$("#totalText").text("");

		$("#launcherMain").show().focus();

		var result = LauncherAPI.getAccountInfoAction();
		
		console.log('Login调试信息:');
		console.log('  - getAccountInfoAction结果:', result);

		if (result && result.Return) {
			ACCOUNT_ID = result.UserNo;
			PERMISSION = result.Permission.toString();
			PRIVILEGE = result.Privilege.toString();
			BANNED = result.Banned;
			REGION = result.Region;
			QA_MODE = PRIVILEGE == QA_PRIVILEGE;
			
			console.log('  - ACCOUNT_ID设置为:', ACCOUNT_ID, '类型:', typeof ACCOUNT_ID);
			console.log('  - REGION设置为:', REGION);
			console.log('  - PERMISSION:', PERMISSION);
		} else {
			console.error('获取账号信息失败:', result);
			Launcher.showError("Internal error: Cannot get account info");
			Launcher.disableLaunchButton("btn-wrong");
			return;
		}

		Launcher.isLoginSuccess = true;

		// 事件监听器已经在页面加载时全局初始化

		Launcher.sendCommand("login|" + ACCOUNT_ID);
		Launcher.sendCommand("check_p");

		Launcher.logAction("signin", "BHS");
	},

	/*
	 * Play button events
	 */
	onButtonClick: function() {
		Launcher.hideMessage();

		switch ($("#playButton").text()) {
			case GAMESTART_BUTTON_STRINGS["btn-gamestart"]:
				Launcher.launchGame();
				break;

			case GAMESTART_BUTTON_STRINGS["btn-update"]:
				Launcher.patchGame();
				break;

			case GAMESTART_BUTTON_STRINGS["btn-repair"]:
				Launcher.repairGame();
				break;

			case GAMESTART_BUTTON_STRINGS["btn-break"]:
				Launcher.abortPatch();
				break;
		}
	},

	enableLaunchButton: function(cls) {
		$("#playButton").empty();
		$("#playButton").addClass("ready");
		$("#playButton").attr("class", "btn " + cls);
		$("#playButton").text(GAMESTART_BUTTON_STRINGS[cls]);
		$("#playButton").text(GAMESTART_BUTTON_STRINGS[cls]);
		$("#repair").show();
	},

	disableLaunchButton: function(cls) {
		$("#playButton").empty();
		$("#playButton").removeClass("ready");
		$("#playButton").attr("class", "btn " + cls);
		$("#playButton").text(GAMESTART_BUTTON_STRINGS[cls]);
		$("#repair").show();
	},

	/*
	 * Repair button event
	 */
	filesCheck: function() {
		if ($("#playButton").hasClass("btn-break")) {
			return false;
		}

		Launcher.repairGame();
	},

	/*
	 * Launcher procedures
	 */
	launchGame: function() {
		Launcher.disableLaunchButton("btn-wait");

		if (!QA_MODE && PERMISSION < 256) {
			var maintenance = LauncherAPI.getMaintenanceStatusAction();

			if (maintenance && maintenance.Return && maintenance.StartTime) {
				Launcher.showError(serverMaintenanceString);
				Launcher.enableLaunchButton("btn-gamestart");
				return;
			}
		}

		if (BANNED) {
			Launcher.showError(accountBlockedString);
			Launcher.disableLaunchButton("btn-wrong");
			return;
		}

		Launcher.status = 3;

		if ((QA_MODE && QA_MODE_NOCHECK) || START_NO_CHECK || PATCH_NO_CHECK) { // no check files in QA mode
			Launcher.status = 0;
			Launcher.sendCommand("execute|" + REGION);

			setTimeout(function() {
				Launcher.logAction("enter_game", "BHS");
			}, 1000);
		} else {
			Launcher.sendCommand("start_p|0");
		}
	},

	patchGame: function() {
		Launcher.enableLaunchButton("btn-break");
		$("#repair").hide();

		Launcher.status = 1;
		Launcher.sendCommand("start_p|1");
	},

	repairGame: function() {
		Launcher.enableLaunchButton("btn-break");
		$("#repair").hide();

		Launcher.status = 2;
		Launcher.sendCommand("start_p|2");
	},

	abortPatch: function() {
		Launcher.disableLaunchButton("btn-wait");
		Launcher.sendCommand("abort_p");
	},

	setRegion: function(region, reload) {
		var language = regionToLanguage(region);

		if (language) {
			LauncherAPI.setAccountLanguageAction(language);

			if (reload) {
				Launcher.goTo("Main");
			}
		}

		REGION = region;
	},

	/*
	 * DPS插件启动
	 */
	launchDpsPlugin: function() {
		console.log('启动DPS插件');
		
		// 检查是否在 Tauri 环境中
		if (typeof window.__TAURI__ !== 'undefined' && window.__TAURI__.invoke) {
			window.__TAURI__.invoke('launch_dps_plugin')
				.then(function(result) {
					console.log('DPS插件启动成功:', result);
					Launcher.showSuccess('DPS插件启动成功');
				})
				.catch(function(error) {
					console.error('DPS插件启动失败:', error);
					Launcher.showError('DPS插件启动失败: ' + error);
				});
		} else {
			console.log('非Tauri环境，无法启动DPS插件');
			Launcher.showError('当前环境不支持DPS插件');
		}
	}
};

/**
 * Launcher L2W hooks
 */
function l2w_getBaseUrl() {
	var patchUrl = PATCH_URL;

	if (!patchUrl) {
		patchUrl = host + "/public/patch";
	}
	if (!patchUrl.match(new RegExp("^http", "i"))) {
		patchUrl = host + patchUrl;
	}

	return patchUrl;
}

function l2w_getLauncherInfoUrl() {
	return l2w_getBaseUrl() + "/launcher_info.ini";
}

function l2w_getServerList() {
	return host + "/tera/ServerList?lang=" + regionToLanguage(REGION).split("-")[0];
}

function l2w_getOTP() {
	var result = LauncherAPI.getAuthKeyAction();

	if (result && result.Return) {
		return result.AuthKey;
	}
}

function l2w_getUserPermission() {
	return PERMISSION;
}

function l2w_getUserCharCnt() {
	var result = LauncherAPI.getCharacterCountAction();

	if (result && result.Return) {
		return result.CharacterCount;
	}
}

function l2w_tooManyRetry() {
	debug("Too many retries on patching");
	return 1;
}

function l2w_checkPatchResult(patch_result, patch_error, file, reason, code) {
	debug(sprintf("Check patch finished with %d %d [%s] %d, %d", patch_result, patch_error, file, reason, code));

	if ((QA_MODE && QA_MODE_NOCHECK) || PATCH_NO_CHECK) { // no check files in QA mode
		Launcher.enableLaunchButton("btn-gamestart");
		return;
	}

	switch (patch_result) {
		case 2: // RESULT_LATEST_VERSION
			Launcher.enableLaunchButton("btn-gamestart");
			break;

		case 3: // RESULT_NEED_UPDATE
			Launcher.enableLaunchButton("btn-update");
			break;

		case 4: // RESULT_NEED_FILE_CHECK
			Launcher.enableLaunchButton("btn-repair");
			break;

		default:
			if (patch_error == 3 || patch_error == 13 || patch_error == 14) {
				Launcher.enableLaunchButton("btn-repair");
			} else {
				Launcher.disableLaunchButton("btn-wrong");
			}

			displayPatchError(patch_error, file, reason, code);
			break;
	}
}

function l2w_patchResult(patch_result, patch_error, file, reason, code) {
	debug(sprintf("Patch finished with %d %d [%s] %d, %d", patch_result, patch_error, file, reason, code));

	switch (patch_result) {
		case 0:
		case 2:
			$("#progressBar1").width("100%");
			$("#progressBar2").width("100%");
			$("#fileName").text("");
			$("#totalText").text("");
			if (Launcher.status == 3) {
				Launcher.disableLaunchButton("btn-wait");
				Launcher.status = 4;
				Launcher.sendCommand("execute|" + REGION);

				setTimeout(function() {
					Launcher.logAction("enter_game", "BHS");
				}, 1000);
			} else {
				Launcher.status = 0;
				Launcher.enableLaunchButton("btn-gamestart");
			}
			break;

		case 1: // patch aborted
			Launcher.status = 0;
			Launcher.sendCommand("check_p");
			break;

		case 3:
			Launcher.status = 0;
			Launcher.enableLaunchButton("btn-update");
			break;

		case 4:
			Launcher.status = 0;
			Launcher.enableLaunchButton("btn-repair");
			break;
		
		case 5:
			Launcher.status = 0;
			Launcher.enableLaunchButton("btn-break");
			$("#repair").hide();
			break;

		default:
			Launcher.status = 0;
			if (patch_error == 3 || patch_error == 13 || patch_error == 14) {
				Launcher.enableLaunchButton("btn-repair");
			} else if (patch_error == 6) {
				Launcher.enableLaunchButton("btn-update");
			} else {
				Launcher.disableLaunchButton("btn-wrong");
			}

			displayPatchError(patch_error, file, reason, code);
			break;
	}
}

function l2w_currentFile(process, file) {
	var patch_info = "";

	switch (process) {
		case 0: // PATCH_FILE_PROCESS_NONE
			patch_info = PATCH_INFO_STRINGS[0];
			break;

		case 1: // PATCH_FILE_PROCESS_DOWNLOAD
			patch_info = PATCH_INFO_STRINGS[1] + ": " + file;
			break;

		case 2: // PATCH_FILE_PROCESS_EXTRACT
			patch_info = PATCH_INFO_STRINGS[2] + ": " + file;
			break;

		case 3: // PATCH_FILE_PROCESS_PATCH
			patch_info = PATCH_INFO_STRINGS[3] + ": " + file;
			break;

		case 4: // PATCH_FILE_PROCESS_HASH
			patch_info = PATCH_INFO_STRINGS[4] + ": " + file;
			break;

		case 5: // PATCH_FILE_PROCESS_DELETE
			patch_info = PATCH_INFO_STRINGS[5] + ": " + file;
			break;

		case 6: // PATCH_FILE_PROCESS_FILE_CHECK
			patch_info = PATCH_INFO_STRINGS[6] + ": " + file;
			break;

		case 11: // PATCH_FILE_PROCESS_MAKE_PATCH_LIST
			patch_info = PATCH_INFO_STRINGS[11];
			break;

		default:
			patch_info = file;
			break;
	}

	$("#fileName").text(patch_info);
}

function l2w_currentProgress(rate, current, total, speed) {
	$("#progressBar1").width(rate + "%");
	// $('#percent').text(sprintf('%d/%d', current, total));
	// $('#incomingAvgSpeed').text(speed);
}

function l2w_totalProgress(rate, current, total) {
	$("#progressBar2").width(rate + "%");
	$("#totalText").text(sprintf("%d / %d", current, total));
}

function l2w_gameEvent(event) {
	debug(sprintf("Game event 0x%X", event));
}

function l2w_gameEnd(end_type1, end_type2) {
	Launcher.gameEnd(end_type1, end_type2);

	Launcher.status = 0;
	// Launcher.sendCommand("close");
	Launcher.enableLaunchButton("btn-gamestart");

	debug(sprintf("Game end 0x%X, 0x%X", end_type1, end_type2));
	displayLauncherError(end_type1, end_type2);
}

function l2w_getExeInfo() {
	return "";
}

function l2w_systemInfoResult(result) {
	debug(result);
}

function l2w_displayInfoResult(result) {
	debug(result);
}

function l2w_openPopup(page_id) {
	debug(page_id);

	if (PAGES_MAP.hasOwnProperty(page_id)) {
		var authKey = l2w_getOTP();

		setTimeout(function() {
			window.open(PAGES_MAP[page_id].replace("%s", authKey));
		}, 0);
	}
}

function l2w_getWebLinkUrl(act_id, param) {
	debug(act_id, param);

	if (ACTS_MAP.hasOwnProperty(act_id)) {
		var authKey = l2w_getOTP();

		return ACTS_MAP[act_id].replace("%s", authKey);
	}
}

// 全局初始化事件监听器
function initializeTauriEventListeners() {
	if (typeof window.__TAURI__ !== 'undefined' && window.__TAURI__.event) {
		console.log('初始化 Tauri 事件监听器');
		
		// 监听执行游戏启动事件
		window.__TAURI__.event.listen('execute_game', function(event) {
			console.log('执行游戏启动事件接收:', event.payload);
			// 启动前检查完成，调用execute命令启动游戏
			Launcher.sendCommand("execute|" + REGION);
		});
		
		// 监听补丁检查结果事件
		window.__TAURI__.event.listen('patch_check_result', function(event) {
			console.log('补丁检查结果事件接收:', event.payload);
			const [patch_result, patch_error, file, reason, code] = event.payload;
			l2w_checkPatchResult(patch_result, patch_error, file, reason, code);
		});
		
		// 监听补丁处理结果事件
		window.__TAURI__.event.listen('patch_result', function(event) {
			console.log('补丁处理结果事件接收:', event.payload);
			const [patch_result, patch_error, file, reason, code] = event.payload;
			l2w_patchResult(patch_result, patch_error, file, reason, code);
		});
		
		// 监听游戏状态变化事件
		window.__TAURI__.event.listen('game_status_changed', function(event) {
			console.log('游戏状态变化事件接收:', event.payload);
			const isRunning = event.payload;
			if (isRunning) {
				// 游戏正在运行
				Launcher.disableLaunchButton("btn-wait");
				$("#fileName").text("游戏正在运行...");
			} else {
				// 游戏已停止
				Launcher.enableLaunchButton("btn-gamestart");
				$("#fileName").text("游戏已停止");
			}
		});
		
		// 监听游戏结束事件
		window.__TAURI__.event.listen('game_ended', function(event) {
			console.log('游戏结束事件接收:', event.payload);
			// 游戏结束后重新启用启动按钮
			Launcher.enableLaunchButton("btn-gamestart");
			$("#fileName").text("游戏已结束");
		});
		
		// 监听游戏状态事件
		window.__TAURI__.event.listen('game_status', function(event) {
			console.log('游戏状态事件接收:', event.payload);
			const status = event.payload;
			$("#fileName").text(status);
		});
		
		// 监听文件检查进度事件
		window.__TAURI__.event.listen('file_check_progress', function(event) {
			console.log('文件检查进度事件接收:', event.payload);
			const progress = event.payload;
			$("#progressBar1").width(progress.progress + "%");
			$("#fileName").text("检查文件: " + progress.current_file);
			$("#totalText").text(progress.current_count + "/" + progress.total_files + " 文件");
		});
		
		// 监听文件检查完成事件
		window.__TAURI__.event.listen('file_check_completed', function(event) {
			console.log('文件检查完成事件接收:', event.payload);
			const result = event.payload;
			$("#progressBar1").width("100%");
			$("#fileName").text("检查完成");
			$("#totalText").text("找到 " + result.files_to_update + " 个需要" + (result.mode === "repair" ? "修复" : "更新") + "的文件");
		});
		
		// 监听下载进度事件
		window.__TAURI__.event.listen('download_progress', function(event) {
			console.log('下载进度事件接收:', event.payload);
			const progress = event.payload;
			$("#progressBar1").width(progress.progress + "%");
			$("#progressBar2").width((progress.downloaded_bytes / progress.total_bytes) * 100 + "%");
			$("#fileName").text("下载: " + progress.file_name);
			$("#totalText").text(progress.current_file_index + "/" + progress.total_files + " 文件");
		});
		
		// 监听下载完成事件
		window.__TAURI__.event.listen('download_complete', function(event) {
			console.log('下载完成事件接收');
			$("#fileName").text("下载完成");
			$("#totalText").text("所有文件已更新");
			$("#progressBar1").width("100%");
			$("#progressBar2").width("100%");
		});
		
		// 监听窗口隐藏事件
		window.__TAURI__.event.listen('window_hidden', function(event) {
			console.log('窗口隐藏事件接收');
			// 可以在这里添加窗口隐藏时的UI处理逻辑
		});
		
		// 监听窗口显示事件
		window.__TAURI__.event.listen('window_shown', function(event) {
			console.log('窗口显示事件接收');
			// 可以在这里添加窗口显示时的UI处理逻辑
		});
		
		// 监听外挂检测事件
		window.__TAURI__.event.listen('cheat_detected', function(event) {
			Launcher.showError(event.payload);
		});

		console.log('Tauri 事件监听器初始化完成');
	}
}

// 页面加载时初始化事件监听器
$(document).ready(function() {
	initializeTauriEventListeners();
});
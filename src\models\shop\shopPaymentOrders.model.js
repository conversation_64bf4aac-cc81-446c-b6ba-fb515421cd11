"use strict";

module.exports = (sequelize, DataTypes) => {
    const PaymentOrders = sequelize.define(
        "shop_payment_orders",
        {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            orderNo: {
                type: DataTypes.STRING(32),
                allowNull: false,
                unique: true,
                comment: "订单号"
            },
            accountDBID: {
                type: DataTypes.INTEGER,
                allowNull: false,
                comment: "用户ID"
            },
            amount: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: false,
                comment: "支付金额"
            },
            status: {
                type: DataTypes.TINYINT,
                allowNull: false,
                defaultValue: 0,
                comment: "支付状态:0=待支付,1=支付成功,2=支付失败,3=已退款"
            },
            paymentMethod: {
                type: DataTypes.STRING(20),
                allowNull: false,
                comment: "支付方式:alipay=支付宝,wechat=微信支付"
            },
            transactionId: {
                type: DataTypes.STRING(64),
                allowNull: true,
                comment: "支付平台交易号"
            },
            paymentTime: {
                type: DataTypes.DATE,
                allowNull: true,
                comment: "支付时间"
            },
            bonusPoints: {
                type: DataTypes.INTEGER,
                allowNull: false,
                defaultValue: 0,
                comment: "赠送积分"
            },
            createdAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updatedAt: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        },
        {
            indexes: [
                {
                    fields: ["accountDBID"]
                },
                {
                    fields: ["status"]
                },
                {
                    fields: ["createdAt"]
                }
            ]
        }
    );

    return PaymentOrders;
};
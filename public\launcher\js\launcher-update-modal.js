/**
 * 启动器更新模态窗口功能
 */
const LauncherUpdateModal = {
	/**
	 * 获取翻译文本
	 * @param {string} key - 翻译键
	 * @param {string} defaultText - 默认文本
	 * @returns {string} 翻译后的文本
	 */
	t(key, defaultText = key) {
		// 使用全局UPDATE_STRINGS变量
		if (typeof UPDATE_STRINGS !== 'undefined' && UPDATE_STRINGS[key]) {
			return UPDATE_STRINGS[key];
		}
		// 否则返回默认文本
		return defaultText;
	},
	/**
	 * 启动时检查更新
	 */
	async checkUpdateOnStart() {
		try {
			// 检查是否在 Tauri 环境中
			if (typeof window.__TAURI__ === 'undefined') {
				console.log('非 Tauri 环境，跳过启动器更新检查');
				return;
			}

			console.log('开始检查启动器更新...');
			
			// 获取当前版本
			const currentVersion = await window.__TAURI__.invoke('get_current_launcher_version');
			$('#current-version').text(currentVersion);
			
			// 检查是否需要更新
			const needsUpdate = await window.__TAURI__.invoke('check_launcher_update_only');
			
			if (needsUpdate) {
				console.log('发现启动器更新，显示更新对话框');
				await this.showModal();
			} else {
				console.log('启动器已是最新版本');
			}
		} catch (error) {
			console.error('检查启动器更新失败:', error);
		}
	},

	/**
	 * 显示更新模态窗口
	 */
	async showModal() {
		try {
			// 获取更新信息
			const currentVersion = await window.__TAURI__.invoke('get_current_launcher_version');
			$('#current-version').text(currentVersion);
			
					// 这里可以获取更新日志，暂时使用静态文本
		$('#latest-version').text(this.t('loading', '获取中...'));
		$('#changelog-content').text(this.t('loadingUpdateInfo', '正在获取更新信息...'));
		
		// 显示模态窗口
		$('#update-modal').fadeIn(300);
		
		// 获取更新信息（包括版本号和更新日志）
		try {
			const updateInfo = await window.__TAURI__.invoke('get_update_info');
			
			$('#latest-version').text(updateInfo.latest_version || this.t('latestVersion', '最新版本'));
			
			// 显示更新日志或默认信息
			if (updateInfo.changelog) {
				$('#changelog-content').html(updateInfo.changelog);
			} else {
				$('#changelog-content').html(`
					<p>• 修复了启动器的一些问题</p>
					<p>• 改进了用户体验</p>
					<p>• 提升了性能和稳定性</p>
					<p>• 添加了新的功能</p>
				`);
			}
		} catch (error) {
			console.error('获取更新信息失败:', error);
			$('#latest-version').text(this.t('latestVersion', '最新版本'));
			$('#changelog-content').html(`
				<p>• 修复了启动器的一些问题</p>
				<p>• 改进了用户体验</p>
				<p>• 提升了性能和稳定性</p>
				<p>• 添加了新的功能</p>
			`);
		}
			
		} catch (error) {
			console.error('显示更新模态窗口失败:', error);
		}
	},

	/**
	 * 确认更新启动器
	 */
	async confirmUpdate() {
		try {
			console.log('开始更新启动器...');
			
					// 禁用按钮
		$('#btn-update-now').prop('disabled', true).text(this.t('updating', '更新中...'));
		$('#btn-update-later').prop('disabled', true);
			
			// 显示进度条
			$('#update-progress').show();
			
			// 监听更新状态
			const unlisten = await window.__TAURI__.event.listen('launcher_update_status', (event) => {
				this.handleUpdateStatus(event.payload);
			});
			
			// 执行更新
			const updated = await window.__TAURI__.invoke('check_and_update_launcher');
			
			// 清理监听
			unlisten();
			
			if (updated) {
				console.log('更新完成，启动器将重新启动');
				$('#update-progress-text').text('更新完成！启动器将重新启动...');
				
				// 3秒后自动关闭（实际上启动器会重启）
				setTimeout(() => {
					this.closeModal();
				}, 3000);
			} else {
				console.log('无需更新');
				this.closeModal();
			}
			
		} catch (error) {
			console.error('更新启动器失败:', error);
			
			// 恢复按钮状态
			$('#btn-update-now').prop('disabled', false).text('立即更新');
			$('#btn-update-later').prop('disabled', false);
			$('#update-progress').hide();
			
			// 显示错误信息
			$('#update-progress-text').text('更新失败: ' + error);
			$('#update-progress').show();
		}
	},

	/**
	 * 处理更新状态
	 */
	handleUpdateStatus(status) {
		console.log('更新状态:', status);
		
		switch (status.status) {
			case 'checking':
				$('#update-progress-text').text(this.t('checkingUpdates', '正在检查更新...'));
				$('#update-progress-fill').css('width', '10%');
				break;
			case 'downloading':
				// 如果有进度信息，显示真实进度
				if (status.progress !== undefined) {
					$('#update-progress-text').text(this.t('downloadingProgress', '正在下载更新文件... (%s%)').replace('%s', status.progress));
					$('#update-progress-fill').css('width', `${status.progress}%`);
				} else {
					$('#update-progress-text').text(this.t('downloadingFile', '正在下载更新文件...'));
					$('#update-progress-fill').css('width', '0%');
				}
				break;
			case 'replacing':
				$('#update-progress-text').text(this.t('replacingFiles', '正在替换启动器文件...'));
				$('#update-progress-fill').css('width', '90%');
				break;
			case 'completed':
				$('#update-progress-text').text(this.t('updateCompleted', '更新完成！正在重启启动器...'));
				$('#update-progress-fill').css('width', '95%');
				break;
			case 'restarting':
				$('#update-progress-text').text(this.t('restarting', '正在重启启动器...'));
				$('#update-progress-fill').css('width', '98%');
				break;
			case 'exit':
				$('#update-progress-text').text(this.t('restartingWait', '重启中，请稍候...'));
				$('#update-progress-fill').css('width', '100%');
				// 可以在这里添加一些视觉效果表示程序即将退出
				break;
			case 'latest':
				$('#update-progress-text').text(this.t('upToDate', '已是最新版本'));
				$('#update-progress-fill').css('width', '100%');
				break;
			case 'error':
				$('#update-progress-text').text(this.t('updateFailed', '更新失败: %s').replace('%s', status.message || this.t('unknownError', '未知错误')));
				$('#update-progress-fill').css('width', '0%');
				// 恢复按钮状态
				$('#btn-update-now').prop('disabled', false).text(this.t('updateNow', '立即更新'));
				$('#btn-update-later').prop('disabled', false);
				break;
		}
	},

	/**
	 * 关闭更新模态窗口
	 */
	closeModal() {
		$('#update-modal').fadeOut(300);
		
		// 重置状态
		$('#btn-update-now').prop('disabled', false).text(this.t('updateNow', '立即更新'));
		$('#btn-update-later').prop('disabled', false);
		$('#update-progress').hide();
		$('#update-progress-fill').css('width', '0%');
	},

	/**
	 * 手动检查更新（可以绑定到按钮）
	 */
	async manualCheckUpdate() {
		try {
			console.log('手动检查启动器更新...');
			
			const needsUpdate = await window.__TAURI__.invoke('check_launcher_update_only');
			
			if (needsUpdate) {
				await this.showModal();
					} else {
			alert(this.t('upToDateAlert', '启动器已是最新版本！'));
		}
	} catch (error) {
		console.error('手动检查更新失败:', error);
		alert(this.t('checkFailed', '检查更新失败: %s').replace('%s', error));
	}
	}
};

// 全局函数，用于向后兼容
function checkLauncherUpdateOnStart() {
	return LauncherUpdateModal.checkUpdateOnStart();
}

function showUpdateModal() {
	return LauncherUpdateModal.showModal();
}

function confirmLauncherUpdate() {
	return LauncherUpdateModal.confirmUpdate();
}

function handleLauncherUpdateStatus(status) {
	return LauncherUpdateModal.handleUpdateStatus(status);
}

function closeUpdateModal() {
	return LauncherUpdateModal.closeModal();
}

function manualCheckLauncherUpdate() {
	return LauncherUpdateModal.manualCheckUpdate();
} 
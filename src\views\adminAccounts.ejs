<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Accounts") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="accountDBID"><%= __("Account ID") %></label>
				<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>">
			</div>
			<div class="form-group">
				<label for="email"><%= __("Email") %></label>
				<input type="email" class="form-control boxed" name="email" value="<%= email %>">
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
	</section>
	<section class="section">
		<a class="btn btn-success" title="" href="/accounts/add"><%= __("Create new account") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th></th>
											<th><%= __("User name") %></th>
											<th><%= __("Registered") %></th>
											<th><%= __("Permission") %></th>
											<th><%= __("Privilege") %></th>
											<th><%= __("Game language") %></th>
											<th><%= __("Last login") %></th>
											<th><%= __("Last server") %></th>
											<th><%= __("Last IP") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

accounts.forEach(account => {
	const banned = account.get("banned")?.get("active") &&
		moment(account.get("banned")?.get("startTime")) < moment() &&
		moment(account.get("banned")?.get("endTime")) > moment();

	const iconClass = banned ?
		"fa fa-ban text-danger" :
		(account.get("online") ? "fa fa-circle text-success" : "fa fa-circle text-danger");

	tableData.push([
		account.get("accountDBID"),
		`<i class="${iconClass}"></i>`,
		`${banned ? "<s>" : ""}<a href="/accounts/edit?accountDBID=${account.get("accountDBID")}">${account.get("userName")}</a>${banned ? "</s>" : ""}`,
		moment(account.get("registerTime")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		(account.get("permission") >= 256 ?
			`<span class="text-primary">${account.get("permission")} <i class="fa fa-wrench"></i></span>` :
			(account.get("permission") >= 1 ?
				`<span class="text-success">${account.get("permission")} <i class="fa fa-level-up"></i></span>` :
				account.get("permission")
			)
		),
		(account.get("privilege") == 32 || account.get("privilege") == 33 ?
			`<span class="text-success">${account.get("privilege")} <i class="fa fa-magic"></i></span>` :
			account.get("privilege")
		),
		(account.get("language") ? __(account.get("language")) : "-"),
		account.get("lastLoginTime") ? moment(account.get("lastLoginTime")).tz(user.tz).format("YYYY-MM-DD HH:mm") : "-",
		account.get("lastLoginServer") ? `(${account.get("lastLoginServer")}) ${account.get("server")?.get("nameString") || ""}` : "-",
		account.get("lastLoginIP") || "-",
		`<a class="btn btn-secondary btn-sm" href="/benefits?accountDBID=${account.get("accountDBID")}"><i class="fa fa-tags"></i></a> ` +
			(account.get("lastLoginServer") ?
				`<a class="btn btn-secondary btn-sm" href="/characters?accountDBID=${account.get("accountDBID")}&amp;serverId=${account.get("lastLoginServer")}"><i class="fa fa-user"></i></a> ` : ""
			) +
			`<a class="btn btn-secondary btn-sm" href="/bans/${account.get("banned") ? "edit" : "add"}?accountDBID=${account.get("accountDBID")}&amp;successUrl=/accounts"><i class="fa fa-ban"></i></a> ` +
			`<a class="btn btn-secondary btn-sm ml-2" href="/accounts/edit?accountDBID=${account.get("accountDBID")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/accounts/delete?accountDBID=${account.get("accountDBID")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			columnDefs: [{ orderable: false, targets: [1, -1] }],
			columns: [null, { className: "col-icon-dot" }, null, null, null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
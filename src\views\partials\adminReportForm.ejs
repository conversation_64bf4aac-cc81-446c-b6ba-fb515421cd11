<section class="section">
	<form class="form-inline" action="" method="GET">
		<div class="form-group">
			<label for="from"><%= __("Period from") %></label>
			<input type="datetime-local" class="form-control boxed" name="from" value="<%= from.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
		</div>
		<div class="form-group">
			<label for="to"><%= __("to") %></label>
			<input type="datetime-local" class="form-control boxed" name="to" value="<%= to.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
		</div>
		<%_ if (accountDBID !== null) { _%>
		<div class="form-group">
			<label for="accountDBID"><%= __("Account ID") %></label>
			<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>">
		</div>
		<%_ } _%>
		<%_ if (servers !== null) { _%>
		<div class="form-group">
			<label for="serverId"><%= __("Server ID") %></label>
			<select class="form-control boxed" name="serverId">
				<option value="">- <%= __("All") %> -</option>
			<%_ servers.forEach(s => { _%>
				<option value="<%= s.get("serverId") %>" <%= s.get("serverId") == serverId ? "selected" : "" %>>(<%= s.get("serverId") %>) <%= s.get("nameString") %></option>
			<%_ }) _%>
			</select>
		</div>
		<%_ } _%>
		<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
	</form>
</section>
{"name": "tera-api", "version": "1.2.2", "description": "Tera API implementation on node.js", "main": "src/app.js", "scripts": {"start_all": "node --expose-gc --max_old_space_size=8192 src/app", "start_admin_panel": "node --expose-gc --max_old_space_size=8192 src/app --component admin_panel", "start_arbiter_api": "node --expose-gc --max_old_space_size=8192 src/app --component arbiter_api", "start_gateway_api": "node --expose-gc --max_old_space_size=8192 src/app --component gateway_api", "start_portal_api": "node --expose-gc --max_old_space_size=8192 src/app --component portal_api"}, "author": "Non-commercial Project 'HSDN' <<EMAIL>>", "keywords": ["mvc", "api", "tera", "shop", "arbiter", "launcher"], "homepage": "https://git.dev.hsdn.org/tera-server/tera-api/", "repository": {"type": "git", "url": "https://git.dev.hsdn.org/tera-server/tera-api.git"}, "bugs": {"url": "https://git.dev.hsdn.org/tera-server/tera-api/issues"}, "dependencies": {"@maxmind/geoip2-node": "^5.0.0", "@msgpack/msgpack": "^3.0.0-beta2", "axios": "^1.10.0", "canvas": "^3.0.0", "cheerio": "^1.1.0", "cls-hooked": "^4.2.2", "commander": "^12.1.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "crc-32": "^1.2.2", "dotenv": "^16.0.1", "easypay-node-sdk": "^1.0.6", "ejs": "^3.1.10", "express": "^5.0.1", "express-ejs-layouts": "^2.5.1", "express-session": "^1.17.3", "express-validator": "^6.15.0", "flexsearch": "^0.7.43", "hcaptcha": "^0.2.0", "i18n": "^0.14.2", "image-size": "^1.2.0", "jose": "^5.9.6", "memorystore": "^1.6.7", "moment-timezone": "^0.5.34", "morgan": "^1.10.0", "morgan-body": "^2.6.6", "multer": "^1.4.5-lts.1", "mysql2": ">=3.11.0", "netmask": "^2.0.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.15", "passport": "^0.6.0", "passport-custom": "^1.1.1", "passport-local": "^1.0.0", "promise-queue": "^2.2.5", "promise-socket": "^7.0.0", "protobufjs": "^7.4.0", "python-struct": "^1.1.3", "queue-promise": "^2.2.1", "rate-limiter-flexible": "^5.0.4", "sequelize": "^6.37.3", "session-file-store": "^1.5.0", "tedious": "^18.6.1", "uuid": "^8.3.2", "winston": "^3.7.2", "xml-js": "^1.6.11", "xmlbuilder": "^15.1.1"}, "devDependencies": {"@types/bencode": "^2.0.4", "@types/cls-hooked": "^4.3.9", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/express": "^5.0.0", "@types/express-ejs-layouts": "^2.5.4", "@types/i18n": "^0.13.12", "@types/morgan": "^1.9.9", "@types/netmask": "^2.0.5", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/passport": "^1.0.17", "@types/promise-queue": "^2.2.3", "@types/python-struct": "^1.0.4", "@types/sequelize": "^4.28.20", "@types/uuid": "^10.0.0"}, "engines": {"node": ">=18.0.0"}, "license": "MIT"}
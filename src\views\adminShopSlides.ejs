<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Shop Slides") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/shop_slides/add"><%= __("Add new slide") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Prio.") %></th>
											<th><%= __("ID") %></th>
											<th><%= __("Background image") %></th>
											<th><%= __("Product ID") %></th>
											<th></th>
											<th><%= __("Product title") %></th>
											<th><%= __("Active") %></th>
											<th><%= __("Published") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

slides.forEach(slide => {
	tableData.push([
		slide.priority,
		slide.id,
		`<a href="/shop_slides/edit?id=${slide.id}"><img src="/static/images/shop-slides-bg/${slide.image}" height="85"></a>`,
		slide.productId,
		slide.product.id ? '<div class="item-icon-block">' +
			`<img data-src="/static/images/tera-icons/${slide.product.icon}.png" class="item-icon-grade-${slide.product.rareGrade} item-icon">` +
			`<img data-src="/static/images/icons/icon_grade_${slide.product.rareGrade}.png" class="item-icon-grade" alt="">` +
			'</div>' : "",
		slide.product.id ? `<a class="item-grade-${slide.product.rareGrade}" href="/shop_products/edit?id=${slide.product.id}">${slide.product.title || __("[unknown]")}</a>` : __("[unknown]"),
		slide.active ? __("Yes") : __("No"),
		`<span class="text-${slide.published ? "success" : "danger"}">${slide.published ? __("Yes") : __("No")}</span>`,
		`<a class="btn btn-secondary btn-sm" href="/shop_slides/edit?id=${slide.id}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/shop_slides/delete?id=${slide.id}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columnDefs: [{ orderable: false, targets: [2, 4, -1] }],
			columns: [{ width: "1%" },  null, null, null, { width: "1%" }, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Send Item Claim Box") %> ID <%= id %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Recipient Information") %></h3>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="accountDBID"><%= __("Account ID") %></label>
								<input type="text" class="form-control boxed" name="accountDBID" value="">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="serverId"><%= __("Server ID") %></label>
								<select class="form-control boxed" name="serverId">
									<option value="">- <%= __("All") %> -</option>
								<%_ servers.forEach(s => { _%>
									<option value="<%= s.get("serverId") %>">(<%= s.get("serverId") %>) <%= s.get("nameString") %></option>
								<%_ }) _%>
								</select>
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="characterId"><%= __("Character ID") %></label>
								<input type="text" class="form-control boxed" name="characterId" value="" placeholder="<%= __("optional") %>">
							</div>
						</div>
						<div class="form-group row"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Box Information") %></h3>
						</div>
						<div class="form-group">
							<label class="control-label"><%= __("Title") %></label>
							<input type="text" class="form-control boxed" value="<%= title %>" disabled>
						</div>
						<div class="form-group">
							<label class="control-label"><%= __("Description") %></label>
							<textarea type="text" class="form-control boxed" disabled><%= content %></textarea>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label"><%= __("Days") %></label>
								<input type="number" class="form-control boxed" value="<%= days %>" disabled>
							</div>
							<div class="col-sm-6">
								<label class="control-label"><%= __("Icon") %></label>
								<div>
									<label>
										<span><img src="/static/images/icons/<%= icon.toLowerCase() %>.png" class="item-icon-form"></span>
									</label>
								</div>
							</div>
						</div>
						<div class="form-group row"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Box Items") %></h3>
						</div>
						<div id="items">
							<div class="form-group row">
								<div class="col-sm-6">
									<label class="control-label"><%= __("Item template ID") %></label>
								</div>
								<div class="col-sm-2">
									<label class="control-label"><%= __("Count") %></label>
								</div>
								<div class="col-sm-4">
									<label class="control-label"><%= __("Service item ID") %></label>
								</div>
							</div>
							<%_ items.forEach(item => { _%>
							<div class="form-group row">
								<div class="col-sm-6">
									<input type="text" class="form-control boxed" name="itemTemplateIds[]" value="<%= item.get("itemTemplateId") %>" disabled>
								</div>
								<div class="col-sm-2">
									<input type="number" class="form-control boxed" value="<%= item.get("boxItemCount") %>" disabled>
								</div>
								<div class="col-sm-3">
									<input type="text" class="form-control boxed" value="<%= item.get("boxItemId") %>" disabled>
								</div>
								<div class="col-sm-1">
									<%_ if (itemChecks.has(item.get("boxItemId"))) { _%>
									<i class="mt-2 fa fa-check text-success"></i>
									<%_ } else { _%>
									<i class="mt-2 fa fa-times text-danger"></i>
									<%_ } _%>
								</div>
							</div>
							<%_ }) _%>
						</div>
						<div class="form-group row"></div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Send") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge;">
<title>Login</title>
<link rel="stylesheet" href="/public/launcher/css/login.css">
<script type="text/javascript" src="/public/launcher/js/jquery-1.11.1.min.js"></script>
<script>
	var PATCH_URL = "<%= patchUrl %>";
</script>
<script type="text/javascript" src="/public/launcher/js/launcher-util.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher-errors.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher.js"></script>
<script>


	function resetPasswordVerifyAction(code, password, password2) {
		if (password != password2) {
			Launcher.showError("<%= __('Two password entries do not match.') %>");
			return;
		}

		var result = LauncherAPI.resetPasswordVerifyAction(code, password);

		if (result) {
			if (result.Return) {
				Launcher.goTo("LoginForm?locale=<%= __req.query.locale %>");
			} else {
				switch (result.ReturnCode) {
					case 10:
						Launcher.showError("<%= __('Please enter a password between 8 and 128 characters long.') %>");
						break;
					case 11:
						Launcher.showError("<%= __('Invalid verification code.') %>");
						break;
					case 12:
						var errorMsg = result.Msg;
						if (result.Msg.includes('Captcha')) {
							errorMsg = "<%= __('Please confirm that you are human.') %>";
						} else {
							errorMsg = "<%= __('The number of verification code entry attempts has been exceeded.') %>";
						}
						Launcher.showError(errorMsg);

						if (!result.Msg.includes('Captcha')) {
							setTimeout(function() {
								Launcher.goTo("LoginForm?locale=<%= __req.query.locale %>");
							}, 3000);
						}
						break;
					default:
						Launcher.showError(result.ReturnCode + ": " + result.Msg);
						console.log(result);
				}
			}
		}
	}

	$(function () {
		$("#userResetPasswordVerifyForm").submit(function() {
			resetPasswordVerifyAction($('#code').val(), $('#password').val(), $('#password2').val());
			return false;
		});

		$("#btnToLogin").click(function() {
			Launcher.goTo("LoginForm?locale=<%= __req.query.locale %>");
		});

		$(window).on("load", function() {
			Launcher.loaded(320, 500);
		});
	});
</script>
</head>
<body oncontextmenu="return false" ondragstart="return false" onselectstart="return false" style="overflow: hidden;">
<div class="wrap">
	<div class="msg-modal" id="msg-modal"><span></span></div>
<%_ if (!captcha) { _%>
	<form class="form-horizontal" name="userResetPasswordVerifyForm"  method="post" action="" id="userResetPasswordVerifyForm" style="padding-top: 20px; display: none;">
<%_ } else { _%>
	<form class="form-horizontal" name="userResetPasswordVerifyForm"  method="post" action="" id="userResetPasswordVerifyForm" style="display: none;">
<%_ } _%>
		<div class="form-group">
			<div class="col">
				<input type="password" class="form-control form-password" id="password" name="password" value="" placeholder="<%= __('New password') %>" maxlength="128" tabindex="2">
			</div>
		</div>
		<div class="form-group">
			<div class="col">
				<input type="password" class="form-control form-password" id="password2" name="password2" value=""  placeholder="<%= __('Confirm password') %>" maxlength="128" tabindex="2">
			</div>
		</div>
		<div class="form-group" style="padding-top: 20px;">
			<div class="col">
				<input type="text" class="form-control form-code" id="code" name="code" value=""  placeholder="<%= __('Verification code') %>" maxlength="128" tabindex="2">
			</div>
		</div>
		<div class="form-group">
			<div class="col" style="text-align: center;">
				<%= __('This code has been sent to your email') %><br>
				<%= email %>
			</div>
		</div>
<%_ if (captcha) { _%>
		<div class="form-group" style="padding-top: 10px; padding-bottom: 5px;">
			<%- captcha %>
		</div>
<%_ } _%>
		<div class="form-group" style="padding-top: 20px;">
			<div class="col" style="text-align: center;">
				<button type="submit" tabindex="3" class="btn-blue"><%= __('Confirm') %></button>
				<br>
				<a href="#" style="text-decoration: underline;" id="btnToLogin"><%= __('Login to another account') %></a>
			</div>
		</div>
	</form>
	<button class="btn-close" onclick="Launcher.sendCommand('close');">Close</button>
</div>
<!--/wrap-->
</body>
</html>
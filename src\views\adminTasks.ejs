<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Admin Tasks Queue") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="handler"><%= __("Task name") %></label>
				<input type="text" class="form-control boxed" name="handler" value="<%= handler %>">
			</div>
			<div class="form-group">
				<label for="tag"><%= __("Task tag") %></label>
				<input type="text" class="form-control boxed" name="tag" value="<%= tag %>">
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
		
	</section>
	<section class="section">
		<a class="btn btn-primary mr-2" title="" href="/tasks/restart"><%= __("Restart tasks queue") %></a>
		<a class="btn btn-warning mr-2" title="" href="/tasks/cancel_failed?handler=<%= handler %>&amp;tag=<%= tag %>" data-toggle="modal" data-target="#confirm-del-modal"><%= __("Cancel failed tasks") %></a>
		<a class="btn btn-danger mr-3" title="" href="/tasks/cancel_all" data-toggle="modal" data-target="#confirm-del-modal"><%= __("Cancel all tasks") %></a>
		<a class="btn btn-secondary" title="" href="/tasks_logs?handler=<%= handler %>&amp;tag=<%= tag %>"><%= __("Show tasks log") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Time") %></th>
											<th><%= __("Task name") %></th>
											<th><%= __("Task tag") %></th>
											<th><%= __("Status") %></th>
											<th><%= __("Message") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

tasks.forEach(task => {
	tableData.push([
		task.get("id"),
		moment(task.get("updatedAt")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss"),
		task.get("handler"),
		task.get("tag") || "-",
		`<span class="text-${{ "0": "primary", "1": "warning", "2": "danger"}[task.get("status")]}">${__({ "0": "Created", "1": "Pending", "2": "Failed"}[task.get("status")])}</span>`,
		task.get("message") || "-",
		`<a class="btn btn-secondary btn-sm" href="/tasks_logs?taskId=${task.get("id")}"><i class="fa fa-file-text-o"></i></a>`,
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
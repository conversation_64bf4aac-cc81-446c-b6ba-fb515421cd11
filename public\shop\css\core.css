body {
	width: 1016px;
	height: 540px;
	background-color: #030406;
	padding-top: 50px;
	padding-bottom: 40px;
}

h2 {
	margin-top: 0;
	font-weight: normal;
}

.sidebar-nav {
	padding: 9px 0;
}

::-webkit-scrollbar-button {
	background-repeat: no-repeat;
	width: 5px;
	height: 0;
}

::-webkit-scrollbar-track {
	background-color: #0C1A28;
}

::-webkit-scrollbar-thumb {
	-webkit-border-radius: 0;
	border-radius: 0;
	background-color: #13426D;
}

::-webkit-scrollbar-thumb:hover {
	background-color: #084bdd;
}

::-webkit-resizer {
	background-image: url();
	background-repeat: no-repeat;
	width: 4px;
	height: 0;
}

::-webkit-scrollbar {
	width: 6px;
}

.height-100 {
	margin-left: 5px!important;
	height: 594px;
	overflow: auto;
}

.main {
	margin: 0 5px;
}

.carousel-item-icon-block {
	width: 64px!important;
	height: 64px!important;
	position: absolute;
	left: 640px;
	top: 200px;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
	border: 3px solid #fff;
}

.item-icon,.item-icon-block {
	display: block;
	width: 64px!important;
	height: 100%!important;
	position: relative;
	left: 0;
	top: 0;
	float: left;
}

.item-icon2,.item-icon-block2 {
	width: 30px!important;
	height: 100%!important;
	position: relative;
	left: 0;
	top: 0;
	float: left;
	-webkit-border-radius: 3px 0 0 3px;
	-moz-border-radius: 3px 0 0 3px;
	border-radius: 3px 0 0 3px;
}

.item-icon {
	display: block;
	height: 64px!important;
	max-height: 64px!important;
	max-width: 64px!important;
	margin-right: 5px;
	-webkit-border-radius: 3px 0 0 0;
	-moz-border-radius: 3px 0 0 0;
	border-radius: 3px 0 0 0;
}

.item-icon2 {
	display: block;
	height: 30px!important;
	max-height: 30px!important;
	max-width: 30px!important;
	margin-right: 5px;
	-webkit-border-radius: 3px 0 0 0;
	-moz-border-radius: 3px 0 0 0;
	border-radius: 3px 0 0 0;
}

.item-icon-grade {
	position: absolute;
	top: 0;
	left: 0;
	width: 16px!important;
	height: 16px!important;
	max-height: 16px!important;
	max-width: 16px!important;
}

.item-icon-grade2 {
	position: absolute;
	top: 0;
	left: 0;
	width: 10px!important;
	height: 10px!important;
	max-height: 10px!important;
	max-width: 10px!important;
}

.item-icon-tag {
	position: absolute;
	top: -1px;
	left: -2px;
	width: 56px!important;
	height: 56px!important;
	max-height: 56px!important;
	max-width: 56px!important;
}

.text-block {
	display: block;
	float: left;
	width: 500px;
	padding: 5px 5px 5px 8px;
	line-height: 16px;
}

.product-header .text-block {
	width: 420px!important;
	padding-top: 0!important;
}

.text-right {
	float: right;
}

.buy-block {
	display: block;
	float: right;
	width: 120px;
	height: 90px!important;
	-webkit-border-radius: 0 3px 3px 0;
	-moz-border-radius: 0 3px 3px 0;
	border-radius: 0 3px 3px 0;
}

.buy-block .btn {
	margin-top: 22px;
}

.well {
	background-color: #051423;
	border: 1px solid #13426D;
}

hr {
	border: 1px solid #2D3640;
}

.well-small {
	padding: 0;
	margin: 0 0 10px;
	min-height: 0;
}

.well-small h5 {
	padding: 0;
	margin: 0 0 6px;
	font-size: 15px;
	line-height: 15px;
}

.btn-info {
	color: #fff;
	text-shadow: 0 -1px 0 rgba(0,0,0,.25);
	background-color: #12203b;
	*background-color: #2f96b4;
	background-image: -moz-linear-gradient(top,#1B5D9A,#13426D);
	background-image: -webkit-gradient(linear,0 0,0 100%,from(#1B5D9A),to(#13426D));
	background-image: -webkit-linear-gradient(top,#1B5D9A,#13426D);
	background-image: -o-linear-gradient(top,#1B5D9A,#13426D);
	background-image: linear-gradient(to bottom,#1B5D9A,#13426D);
	background-repeat: repeat-x;
	border-color: #13426D #13426D #1B5D9A;
	border-color: rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff5bc0de', endColorstr='#ff2f96b4', GradientType=0);
	filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
}

.btn-info.active,.btn-info.disabled,.btn-info:active,.btn-info:focus,.btn-info:hover,.btn-info[disabled] {
	color: #fff;
	background-color: #13426D;
	*background-color: #13426D;
}

.btn2 {
	display: inline-block;
	*display: inline;
	padding: 4px 12px;
	margin-bottom: 0;
	*margin-left: .3em;
	font-size: 14px;
	line-height: 20px;
	color: #ccc;
	text-align: center;
	text-shadow: 0 1px 1px rgba(18, 32, 59, 0.75);
	vertical-align: middle;
	cursor: pointer;
	background-color: #13426D;
	*background-color: #12203B;
	background-image: -moz-linear-gradient(top,#13426D,#12203B);
	background-image: -webkit-gradient(linear,0 0,0 100%,from(#13426D),to(#12203B));
	background-image: -webkit-linear-gradient(top,#13426D,#12203B);
	background-image: -o-linear-gradient(top,#13426D,#12203B);
	background-image: linear-gradient(to bottom,#13426D,#12203B);
	background-repeat: repeat-x;
	border: 1px solid #12203B;
	border-top-color: rgb(18, 32, 59);
	border-right-color: rgb(18, 32, 59);
	border-bottom-color: rgb(18, 32, 59);
	border-left-color: rgb(18, 32, 59);
	*border: 0;
	border-color: #12203B #12203B #12203B;
	border-color: rgba(0,0,0,.1) rgba(0,0,0,.1) rgba(0,0,0,.25);
	border-bottom-color: rgba(0, 0, 0, 0.25);
	border-bottom-color: #12203B;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe6e6e6', GradientType=0);
	filter: progid:DXImageTransform.Microsoft.gradient(enabled=false);
	*zoom: 1;
	-webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.2),0 1px 2px rgba(0,0,0,.05);
	-moz-box-shadow: inset 0 1px 0 rgba(255,255,255,.2),0 1px 2px rgba(0,0,0,.05);
	box-shadow: inset 0 1px 0 rgba(255,255,255,.2),0 1px 2px rgba(0,0,0,.05);
}

.btn2:focus, .btn2:hover, .btn2:active {
	background-color: #12203B;
	*background-color: #2D3640;
	border: 1px solid #12203B;
	color: #ccc;
}

.navbar input[type="text"] {
	color: #fff;
	background-color: #131318;
	border: 1px solid #334150;
	width: 130px;
}

.navbar button {
	margin-right: 10px;
}

.gold {
	padding-top: 1px;
	line-height: 28px;
	color: gold;
	font-size: 16px;
	text-shadow: 0 -1px 0 rgba(0,0,0,.35);
}

.red-color {
	background-color: #d63e3e;
}

.gold2 {
	color: gold;
	text-shadow: 0 -1px 0 rgba(0,0,0,.35);
}

.white-text {
	color: #fff!important;
}

.black-text {
	color: #000!important;
}

a, a:focus {
	color: #fff;
	text-decoration: none;
}

.nav-list>.active>a,.nav-list>.active>a:focus,.nav-list>.active>a:hover {
	color: #f8f9fa!important;
	text-shadow: 0 -1px 0 rgba(0,0,0,.2);
	background-color: #13426D!important;
}

.nav-list>li>a:hover {
	color: #081b2c!important;
	background-color: #fff!important;
}

.nav-list>li>a,.nav-list>li>a:focus {
	color: #f8f9fa;
	background-color: transparent;
}

.nav-list>li>a:focus {
	background-color: transparent;
}

.item_grade_0 {
	color: #fff!important;
}

.modal,.text-light {
	color: #f8f9fa!important;
}

.modal {
	background-color: #13426D;
}

.close {
	color: #fff!important;
	text-shadow: 0 1px 0 #fff;
}

.modal-footer,.modal-header {
	border: 1px solid #13426D;
}

.modal-footer {
	background-color: #1B5D9A;
	-webkit-box-shadow: inset 0 1px 0 #12203b;
	-moz-box-shadow: inset 0 1px 0 #12203b;
	box-shadow: inset 0 1px 0 #12203b;
}

.modal-lg,.modal-xl {
	width: 650px;
}

.hero-unit,.thumbnail {
	border: 1px solid #13426D;
}

.table td,.table th {
	border-top: 1px solid #2D3640;
}

.hero-unit {
	padding: 30px;
	margin-bottom: 10px;
	background-color: #051423;
}

.container-fluid {
	max-width: 976px;
}

.container-fluid2 {
	margin-left: 5px;
}

.chip {
	height: 30px;
	padding: 0 12px;
	margin-top: 5px;
	margin-right: 8px;
	margin-bottom: 0;
	font-weight: 500;
	line-height: 30px;
	color: rgba(0,0,0,.6);
	cursor: pointer;
	background-color: #10365A;
	border-radius: 16px;
	-webkit-transition: all .3s linear;
	transition: all .3s linear;
}

.chip img {
	float: left;
	width: 32px;
	height: 32px;
	margin: 0 8px 0 -12px;
	border-radius: 50%;
}

.text-success {
	color: #0f6;
}

#promoForm {
	margin: 0 !important;
}

#promoForm legend {
	border-bottom: 1px solid #2D3640;
	font-size: 32px;
	margin-bottom: 0;
}

#promoForm label {
	padding-top: 15px;
	font-size: 18px;
}

#promoForm input[type=text] {
	color: #fff;
	background-color: #131318;
	border: 1px solid #334150;
	font-size: 24px;
	line-height: 30px;
	padding: 10px;
	width: 300px;
}

.alert {
	margin-bottom: 10px;
}

.carousel {
	margin: 10px 5px 0;
}

.carousel-caption {
	padding: 200px;
	background: #333;
	background: rgba(0,0,0,.25);
}

.carousel-inner {
	border-radius: 6px;
}

.hero-unit hr {
	margin: 10px 0;
}

.carousel-control {
	top: 45%;
}

.buy-block-left, .buy-block-right {
	margin: 0!important;
	height: 130px;
	width: 70%!important;
	border-radius: 6px 0 0 6px;
	background-color: #0E3756;
}

.buy-block-left {
	padding: 12px 5px 5px 14px;
	font-size: 12px;
	line-height: 18px;
}

.buy-block-left .btn-small {
	line-height: 14px;
	margin-top: -4px;
}

.buy-block-left legend {
	line-height: 24px!important;
	margin-top: 3px;
	margin-bottom: 4px;
	border-bottom: 0;
}

.buy-block-right .btn-small {
	line-height: 14px;
	margin-bottom: 4px;
}

.buy-block-right {
	width: 30%!important;
	text-align: center;
	padding: 9px 16px;
	border-radius: 0 6px 6px 0;
	background-color: #4d1e1e;
}

.buy-descr .buy-descr-header {
	font-size: 13px;
	line-height: 22px;
	margin-bottom: 10px;
}

.coupon-form input {
	font-size: 16px;
	font-weight: bold;
	font-family: Verdana, Geneva, Tahoma, sans-serif;
	letter-spacing: 1px;
}

/* Removed ::placeholder for compatibility */

.coupon-form input::-webkit-input-placeholder {
	font-weight: normal;
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.coupon-form input::-moz-placeholder {
	font-weight: normal;
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.coupon-form input:-ms-input-placeholder {
	font-weight: normal;
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.coupon-form input::-ms-input-placeholder {
	font-weight: normal;
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.coupon-form .coupon-error {
	margin-right: 9px;
	padding: 1px 8px;
}

.coupon-form .coupon-descr {
	margin-top: 5px;
}


.buy-gift-form .custom-select {
	min-width: 150px !important;
}

.buy-gift-form .custom-select-selected {
	font-weight: normal;
	background-color: white;
	color: #555;
	border: 1px solid #ccc;
}

.buy-gift-form .custom-select-arrow {
	color: #555;
}

.buy-gift-form .custom-select-option:hover {
	border: 1px solid #767676;
}

.buy-gift-form .gift-error {
	margin-right: 9px;
	padding: 1px 8px;
}

.buy-gift-form .gift-descr {
	margin-top: 5px;
}

.quantity {
	width: 37px!important;
	height: 32px!important;
	font-size: 18px!important;
	margin-top: 10px;
	margin-right: 8px;
}

.buy-info {
	margin: 0!important;
	padding: 0!important;
}

.recipient-info {
	float: left;
	width: 100%;
	margin-top: 10px;
	margin-bottom: 20px;
	padding: 10px 0;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
}

.recipient-info-left {
	float: left;
	padding-left: 12px;
	padding-top: 3px;
}

.recipient-info-right {
	float: right;
	text-align: center;
	padding-right: 12px;
}

.name {
	vertical-align: middle !important;
}

.promocode-form input {
	font-family: Verdana, Geneva, Tahoma, sans-serif;
	letter-spacing: 1px;
	width: 350px!important;
}

.promocode-form input::-webkit-input-placeholder {
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.promocode-form input::-moz-placeholder {
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.promocode-form input:-ms-input-placeholder {
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.promocode-form input::-ms-input-placeholder {
	font-family: Arial, Helvetica, sans-serif;
	letter-spacing: normal;
}

.promocode-list td {
	vertical-align: middle;
	padding: 15px 30px 15px 10px;
}

.promocode-list .promocode {
	font-size: 24px;
	line-height: 30px;
}

.promocode-list .date {
	font-size: 14px;
}

.coupons-list {
	overflow: hidden;
	margin: 0;
	padding: 0;
}

.coupons-list .coupon-box {
	float: left;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	margin: 4px 8px 4px 0;
	padding: 12px 5px 10px 5px;
	width: 216px;
	text-align: center;
	color: #fff;
	text-shadow: 0 -1px 0 rgba(0, 0, 0, .25);
	background-color: #13426D;
	background-image: -moz-linear-gradient(top, #1B5D9A, #13426D);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#1B5D9A), to(#13426D));
	background-image: -webkit-linear-gradient(top, #1B5D9A, #13426D);
	background-image: -o-linear-gradient(top, #1B5D9A, #13426D);
	background-image: linear-gradient(to bottom, #1B5D9A, #13426D);
	background-repeat: repeat-x;
	border-color: #13426D #13426D #1B5D9A;
	border-color: rgba(0, 0, 0, .1) rgba(0, 0, 0, .1) rgba(0, 0, 0, .25);
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	cursor: default;
}

.coupons-list .coupon-box.used {
	background-image: none;
	opacity: .65;
	filter: alpha(opacity=65);
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none
}

.coupons-list .coupon-box:after {
	content: "";
	display: block;
	clear: both;
}

.coupons-list .coupon {
	font-family: Arial, Helvetica, sans-serif;
	font-weight: bold;
	letter-spacing: 1px;
	font-size: 24px;
	line-height: 24px;
	margin-bottom: 2px;
	cursor: pointer;
}

.coupons-list .coupon span {
	position: relative;
	top: 4px;
}

.coupons-list .coupon i {
	position: relative;
	font-size: 14px;
	top: -8px;
	margin-left: 5px;
}

.coupons-list .discount {
	margin-bottom: 5px;
}

.coupons-list .date {
	font-size: 14px;
	line-height: 22px;
}

.discount-badge {
	position: relative;
	top: -1px;
	margin-left: 3px;
	margin-top: 5px;
	background: #cc3333;
}

.user-discount-badge {
	position: relative;
	top: -1px;
	margin-left: 3px;
	margin-top: 5px;
	background: #e08a00;
}

.icon_grade_0 {
	background: #020024;
	background: linear-gradient(135deg,#020024 0,#283040 0,#0b1420 100%);
}

.icon_grade_1 {
	background: #185438;
	background: linear-gradient(135deg,#185438 0,#185438 0,#10281d 100%);
}

.icon_grade_2 {
	background: #104878;
	background: linear-gradient(135deg,rgba(16,72,120) 0,#104878 0,#082040 100%);
}

.icon_grade_3 {
	background: #a07008;
	background: linear-gradient(135deg,#a07008 0,#a07008 0,#381800 100%);
}

.icon_grade_4 {
	background: #73197b;
	background: linear-gradient(135deg,#73197b 0,#73197b 0,#200428 100%);
}

.item_grade_0, .icon_grade_0:hover {
	color: #fff !important;
}

.item_grade_1, .icon_grade_1:hover {
	color: #1fa717 !important;
}

.item_grade_2, .icon_grade_2:hover {
	color: #0391c4 !important;
}

.item_grade_3, .icon_grade_3:hover {
	color: #ffa414 !important;
}

.item_grade_4, .icon_grade_4:hover {
	color: #c63db6 !important;
}

.item_grade_5, .icon_grade_5:hover {
	color: orange !important;
}

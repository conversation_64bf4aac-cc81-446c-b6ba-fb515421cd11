<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Online Users") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="serverId"><%= __("Server ID") %></label>
				<select class="form-control boxed change-submit" name="serverId">
					<option value="">- <%= __("All") %> -</option>
				<%_ servers.forEach(s => { _%>
					<option value="<%= s.get("serverId") %>" <%= s.get("serverId") == serverId ? "selected" : "" %>>(<%= s.get("serverId") %>) <%= s.get("nameString") %></option>
				<%_ }) _%>
				</select>
			</div>
		</form>
	</section>
	<section class="section">
		<a class="btn btn-danger<%- isNaN(serverId) || serverId === "" || online.length == 0 ? " disabled" : "" %>" title="" href="/online/kick_all?serverId=<%= serverId %>&amp;fromServerId=<%= serverId %>"><%= __("Kick all online players") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Enter game") %></th>
											<th><%= __("Server ID") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Client IP") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

online.forEach(account => {
	tableData.push([
		moment(account.get("info").get("lastLoginTime")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		account.get("serverId") || "-",
		(account.get("info").get("permission") >= 256 ?
			`<span class="text-primary">${account.get("accountDBID")} <i class="fa fa-wrench"></i></span>` :
			(account.get("info").get("permission") >= 1 ?
				`<span class="text-success">${account.get("accountDBID")} <i class="fa fa-level-up"></i></span>` :
				account.get("accountDBID")
			)
		),
		account.get("info").get("userName"),
		account.get("info").get("lastLoginIP"),
		`<a class="btn btn-secondary btn-sm" href="/online/kick?accountDBID=${account.get("accountDBID")}&amp;serverId=${account.get("serverId")}&amp;fromServerId=${serverId || ""}"><i class="fa fa-times"></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
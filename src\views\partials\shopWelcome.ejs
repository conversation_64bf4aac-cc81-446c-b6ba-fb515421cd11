<%_
const activeBenefits = benefits.filter(b => !moment().isAfter(b.get("availableUntil")));

const founder = activeBenefits.filter(b => shopConfig.founderBenefits.map(a => parseInt(a)).includes(b.get("benefitId")));
const premium = activeBenefits.filter(b => shopConfig.premiumBenefits.map(a => parseInt(a)).includes(b.get("benefitId")));
const pcCafe = activeBenefits.filter(b => shopConfig.pcCafeBenefits.map(a => parseInt(a)).includes(b.get("benefitId")));
_%>
<div class="main hero-unit white-text">
	<h2><%= __("Welcome!") %></h2><hr>
	<%_ if (server !== null) { _%>
	<%= __("You are playing on the server") %>: <b><%= server.get("nameString") %></b><br>
	<%_ } _%>
	<%_ if (premium.length) { _%>
	<%_ const premiumDays = moment(premium[0].get("availableUntil")).diff(moment(), "days"); _%>
	<%- __("Premium status account") %>: <b><%= __("Yes") %></b><%- premiumDays < 365 ? ` (${__("%s days left", `<b>${premiumDays}</b>`)})` : "" %><br>
	<%_ } else { _%>
	<%- __("Premium status account") %>: <b><%= __("No") %></b><br>
	<%_ } _%>
	<%_ if (pcCafe.length) { _%>
	<%_ const pcCafeDays = moment(pcCafe[0].get("availableUntil")).diff(moment(), "days"); _%>
	<%- __("PC Cafe account") %>: <b><%= __("Yes") %></b><%- pcCafeDays < 365 ? ` (${__("%s days left", `<b>${pcCafeDays}</b>`)})` : "" %><br>
	<%_ } else { _%>
	<%- __("PC Cafe account") %>: <b><%= __("No") %></b><br>
	<%_ } _%>
	<%- __("Founder account") %>: <b><%= founder.length ? __("Yes") : __("No") %></b>
</div>
<%_ if (slides.length) { _%>
<div id="carousel" class="carousel slide">
	<div class="carousel-inner">
		<%_ let i = 0; slides.forEach(slide => { i++; _%>
		<div class="item<%= i == 1 ? " active" : "" %>">
			<a href="#" data-id="<%= slide.get("productId") %>" class="carousel-item"><img src="/public/shop/images/shop-slides-bg/<%= slide.get("image") %>" alt="" class="img-rounded" style="width: 100%;"></a>
		</div>
		<%_ }) _%>
	</div>
	<a class="left carousel-control" href="#carousel" data-slide="prev" style="display: none;">&lsaquo;</a>
	<a class="right carousel-control" href="#carousel" data-slide="next" style="display: none;">&rsaquo;</a>
</div>
<%_ } _%>
<script type="text/javascript">
	$(function() {
		$("#carousel").carousel();
		$("#carousel .carousel-item").click(function () {
			if ($(this).data("id")) {
				loadContentProduct({ id: $(this).data("id"), back: false });
			}
		});
		$("#carousel").imagesLoaded(function() {
			$(".carousel-control").show();
		});
	});
</script>
{"_box_title_": "Your shop purchase", "_box_content_": "Greetings adventurer. Here is your new item! Thank you for your order and good luck in your quests.", "TERA Shop": "TERA Shop", "Home": "Home", "Promo Code": "Promo Code", "Coupons": "Coupons", "Search": "Search", "Buy": "Buy", "Back": "Back", "pc.": "pc.", "Possible items": "Possible items", "For female characters only.": "For female characters only.", "For male characters only.": "For male characters only.", "For races": "For races", "For classes": "For classes", "Can be trade with other players.": "Can be trade with other players.", "Cannot be transferred to other players.": "Cannot be transferred to other players.", "Can't put it in the warehouse.": "Can't put it in the warehouse.", "Minimum character level": "Minimum character level", "Your price": "Your price", "You don't have enough funds to buy.": "You don't have enough funds to buy.", "You have success purchased an item.": "You have success purchased an item.", "Purchase in progress": "Purchase in progress", "Spent": "Spent", "An error occurred while purchasing the product": "An error occurred while purchasing the product", "Click the &quot;Buy&quot; button to get the item:": "Click the &quot;Buy&quot; button to get the item:", "Your account will be debited": "Your account will be debited", "The item will appear in the &quot;Item Claim&quot; window.": "The item will appear in the &quot;Item Claim&quot; window.", "You can open it from the main menu in the &quot;Shop&quot; section the &quot;Item Claim&quot; option.": "You can open it from the main menu in the &quot;Shop&quot; section the &quot;Item Claim&quot; option.", "Enter a search keyword that is at least 3 letters long.": "Enter a search keyword that is at least 3 letters long.", "No products were found matching your request. Try looking for something else.": "No products were found matching your request. Try looking for something else.", "Unfortunately, TERA Shop is currently closed.": "Unfortunately, TERA Shop is currently closed.", "An error occurred while running the store. Try closing the window and open again.": "An error occurred while running the store. Try closing the window and open again.", "Welcome!": "Welcome!", "You are playing on the server": "You are playing on the server", "Founder account": "Founder account", "Premium status account": "Premium status account", "PC Cafe account": "PC Cafe account", "%s days left": "%s days left", "Yes": "Yes", "No": "No", "%s% off": "%s% off", "Discount: %s%": "Discount: %s%", "Redeem Promo Code": "Redeem Promo Code", "Enter promo code": "Enter promo code", "promo code": "promo code", "Promo code activated successfully!": "Promo code activated successfully!", "Promo code must be entered with the signs of separation.": "Promo code must be entered with the signs of separation.", "Activate": "Activate", "Activated Promo Codes": "Activated Promo Codes", "Please enter your promo code.": "Please enter your promo code.", "The specified promo code does not exist.": "The specified promo code does not exist.", "The specified promo code has expired.": "The specified promo code has expired.", "The specified promo code has reached its activation limit.": "The specified promo code has reached its activation limit.", "The specified promo code has already been activated.": "The specified promo code has already been activated.", "You have no coupons available.": "You have no coupons available.", "Available Coupons": "Available Coupons", "Used Coupons": "Used Coupons", "Available until:": "Available until:", "Used at:": "Used at:", "Use coupon": "Use coupon", "discount applied": "discount applied", "Use Coupon": "Use Coupon", "Choose from my coupons": "Choose from my coupons", "Enter coupon manually": "Enter coupon manually", "Enter your coupon": "Enter your coupon", "Here you can enter your coupon, which will give you a discount on this purchase.": "Here you can enter your coupon, which will give you a discount on this purchase.", "Accept": "Accept", "Cancel": "Cancel", "Please enter your coupon.": "Please enter your coupon.", "The specified coupon does not exist.": "The specified coupon does not exist.", "The specified coupon has expired.": "The specified coupon has expired.", "The specified coupon has reached its activation limit.": "The specified coupon has reached its activation limit.", "The specified coupon has already been activated.": "The specified coupon has already been activated.", "Activation error": "Activation error", "Buy as a gift": "Buy as a gift", "Buy a Product as a Gift": "Buy a Product as a Gift", "Enter character nickname": "Enter character name", "Please enter character nickname.": "Please enter character nickname.", "Enter the nickname of the character you want to buy the product for as a gift.": "Enter the nickname of the character you want to buy the product for as a gift.", "This product will be sent as a gift to the character:": "This product will be sent as a gift to the character:", "lvl.": "lvl.", "The specified character was not found.": "The specified character was not found.", "The specified character belongs to you.": "The specified character belongs to you.", "Search error": "Search error", "Too many requests.": "Too many requests.", "warrior": "Warrior", "lancer": "Lancer", "slayer": "Slayer", "berserker": "<PERSON><PERSON><PERSON><PERSON>", "sorcerer": "Sorcerer", "archer": "<PERSON>", "priest": "Priest", "elementalist": "Mystic", "soulless": "Reaper", "engineer": "<PERSON>", "fighter": "Brawler", "assassin": "Ninja", "glaiver": "Valkyrie", "human": "Human", "highelf": "High Elf", "aman": "<PERSON><PERSON>", "castanic": "<PERSON><PERSON><PERSON>", "popori": "Pop<PERSON>", "elin": "<PERSON><PERSON>", "baraka": "<PERSON><PERSON>", "[unknown]": "[unknown]", "Recharge": "Recharge"}
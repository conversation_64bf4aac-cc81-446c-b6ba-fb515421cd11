<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Account Benefits List") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="accountDBID"><%= __("Account ID") %></label>
				<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>">
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
	</section>
	<section class="section">
		<a class="btn btn-success" title="" href="/benefits/add?accountDBID=<%= accountDBID %>"><%= __("Add new benefit") %></a>
	</section>
	<%_ if (benefits === null) { _%>
	<section class="section">
		<div class="alert alert-info">
			<%= __("Please enter account ID.") %>
		</div>
	</section>
	<%_ } else { _%>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Benefit ID") %></th>
											<th><%= __("Benefit description") %></th>
											<th><%= __("Available until") %></th>
											<th><%= __("Active") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
	<%_ } _%>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

(benefits || []).forEach(benefit => {
	const active = moment(benefit.get("availableUntil")) > moment(); 

	tableData.push([
		benefit.get("accountDBID"),
		benefit.get("info")?.get("userName") || "-",
		benefit.get("benefitId"),
		accountBenefits?.getOne(benefit.get("benefitId"))?.string || "-",
		moment(benefit.get("availableUntil")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		`<span class="text-${active ? "success" : "danger"}">${active ? __("Yes") : __("No")}</span>`,
		`<a class="btn btn-secondary btn-sm" href="/benefits/edit?benefitId=${benefit.get("benefitId")}&amp;accountDBID=${benefit.get("accountDBID")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/benefits/delete?benefitId=${benefit.get("benefitId")}&amp;accountDBID=${benefit.get("accountDBID")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
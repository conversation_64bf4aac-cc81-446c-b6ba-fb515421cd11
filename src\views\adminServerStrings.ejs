<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Server List Strings") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success<%- strings.length >= availableLanguages.length ? " disabled" : "" %>" title="" href="/server_strings/add"><%= __("Add new strings") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Language code") %></th>
											<th><%= __("Category PvE") %></th>
											<th><%= __("Category PvP") %></th>
											<th><%= __("Server offline") %></th>
											<th><%= __("Server low") %></th>
											<th><%= __("Server medium") %></th>
											<th><%= __("Server high") %></th>
											<th><%= __("Crowdness no") %></th>
											<th><%= __("Crowdness yes") %></th>
											<th><%= __("Popup") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

strings.forEach(string => {
	tableData.push([
		`(${string.get("language")}) ${__(string.get("language"))}`,
		string.get("categoryPvE"),
		string.get("categoryPvP"),
		string.get("serverOffline"),
		string.get("serverLow"),
		string.get("serverMedium"),
		string.get("serverHigh"),
		string.get("crowdNo"),
		string.get("crowdYes"),
		string.get("popup"),
		`<a class="btn btn-secondary btn-sm" href="/server_strings/edit?language=${string.get("language")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/server_strings/delete?language=${string.get("language")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [{ className: "text-nowrap" }, null, null, null, null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
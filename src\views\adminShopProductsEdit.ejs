<%_
const addInfoShow = icon || rareGrade !== null || Object.values(title).join("") || Object.values(description).join("");
const discountInfoShow = discount > 0;
const tagInfoShow = tag !== null;
_%>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Shop Product") %> ID <%= id %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Product Information") %></h3>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="price"><%= __("Price") %></label>
								<input type="number" class="form-control boxed" name="price" value="<%= price %>" min="0" max="100000000">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="userName"><%= __("Category ID") %></label>
								<select class="form-control boxed" name="categoryId">
								<%_ categories.forEach(category => { _%>
									<option value="<%= category.get("id") %>" <%= categoryId == category.get("id") ? 'selected' : "" %>>(<%= category.get("id") %>) <%= category.get("strings")[0]?.get("title") || __("[unknown]") %></option>
								<%_ }) _%>
								</select>
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="validAfter"><%= __("Valid from") %></label>
								<input type="datetime-local" class="form-control boxed" name="validAfter" value="<%= validAfter.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="validBefore"><%= __("Valid to") %></label>
								<input type="datetime-local" class="form-control boxed" name="validBefore" value="<%= validBefore.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="sort"><%= __("Sort") %></label>
								<input type="number" class="form-control boxed" name="sort" value="<%= sort %>" min="-100000000" max="100000000">
							</div>
							<div class="col-sm-6 mt-4">
								<label>
									<input type="checkbox" class="checkbox" name="active" <%- active ? "checked=\"checked\"" : "" %>>
									<span><%= __("Active") %></span>
								</label>
							</div>
						</div>
						<div class="title-block mt-4">
							<h3 class="title"><%= __("Product Items") %></h3>
						</div>
						<div id="items">
							<%_ itemTemplateIds.forEach((itemTemplateId, i) => { _%>
							<div class="form-group row">
								<div class="col-sm-7">
									<label class="control-label" for="itemTemplateIds[]">
										<%= __("Item template ID") %>
										<a class="btn btn-secondary btn-sm remove-item" title="" href="#"><i class="fa fa-minus"></i></a>
									</label>
									<input type="text" class="form-control boxed" name="itemTemplateIds[]" value="<%= itemTemplateIds[i] %>">
								</div>
								<div class="col-sm-2">
									<label class="control-label" for="boxItemCounts[]"><%= __("Count") %></label>
									<input type="number" class="form-control boxed mt-2" name="boxItemCounts[]" value="<%= boxItemCounts[i] %>" min="1" max="1000000">
								</div>
								<div class="col-sm-3">
									<label class="control-label" for="boxItemIds[]"><%= __("Service item ID") %></label>
									<input type="number" class="form-control boxed mt-2" name="boxItemIds[]" value="<%= boxItemIds[i] %>" placeholder="<%= __("optional") %>" min="1" max="100000000">
								</div>
							</div>
							<%_ }) _%>
						</div>
						<div class="form-group">
							<button type="button" class="btn btn-secondary" id="add-item"><i class="fa fa-plus"></i> <%= __("Add item") %></button>
						</div>
						<div id="additional-info" class="form-panel mt-4" role="tablist">
							<div class="form-panel-heading title-block <%= addInfoShow ? "active" : "" %>" role="tab">
								<h3 class="title" data-toggle="collapse" data-target="#additional-info-content" role="button">
									<%= __("Additional Information") %>
								</h3>
							</div>
							<div id="additional-info-content" class="form-panel-collapse collapse <%= addInfoShow ? "show" : "" %>" role="tabpanel">
								<div class="form-panel-body">
									<div class="form-group row">
										<div class="col-sm-6">
											<label class="control-label" for="icon"><%= __("Icon") %> <span class="text-secondary">(<%= __("example") %>: gift_box01_tex)</span></label>
											<input type="text" class="form-control boxed" name="icon" value="<%= icon %>" placeholder="<%= __("optional") %>">
										</div>
										<div class="col-sm-6">
											<label class="control-label" for="rareGrade"><%= __("Rare grade") %></label>
											<select class="form-control boxed" name="rareGrade">
												<option value="">- <%= __("None") %> -</option>
												<option value="0" class="item-grade-0" <%= rareGrade === 0 ? "selected" : "" %>>(0) <%= __("Common") %></option>
												<option value="1" class="item-grade-1" <%= rareGrade === 1 ? "selected" : "" %>>(1) <%= __("Uncommon") %></option>
												<option value="2" class="item-grade-2" <%= rareGrade === 2 ? "selected" : "" %>>(2) <%= __("Rare") %></option>
												<option value="3" class="item-grade-3" <%= rareGrade === 3 ? "selected" : "" %>>(3) <%= __("Superior") %></option>
												<option value="4" class="item-grade-4" <%= rareGrade === 4 ? "selected" : "" %>>(4) <%= __("Mythic") %></option>
											</select>
										</div>
									</div>
									<div class="form-group row">
										<div class="col-sm-6">
											<div id="itemIcons"></div>
										</div>
									</div>
									<div class="form-group">
										<label class="control-label"><%= __("Title") %></label>
										<%_ languages.forEach(language => { _%>
										<div class="form-group row">
											<div class="col-sm-2 mt-2">
												<label for="title[<%= language %>]" class="control-label"><small><%= __(language) %></small></label>
											</div>
											<div class="col-sm-10">
												<input type="text" class="form-control boxed" name="title[<%= language %>]" value="<%= title[language] || "" %>" placeholder="<%= __("optional") %>">
											</div>
										</div>
										<%_ }) _%>
									</div>
									<div class="form-group">
										<label class="control-label"><%= __("Description") %></label>
										<%_ languages.forEach(language => { _%>
										<div class="form-group row">
											<div class="col-sm-2 mt-2">
												<label for="description[<%= language %>]" class="control-label"><small><%= __(language) %></small></label>
											</div>
											<div class="col-sm-10">
												<input type="text" class="form-control boxed" name="description[<%= language %>]" value="<%= description[language] || "" %>" placeholder="<%= __("optional") %>">
											</div>
										</div>
										<%_ }) _%>
									</div>
									<div class="form-group row"></div>
									<div class="form-group alert alert-secondary">
										<%= __("If additional information is not set, the information for the product will be taken from the first item added.") %>
									</div>
								</div>
							</div>
						</div>
						<div id="tag-info" class="form-panel" role="tablist">
							<div class="form-panel-heading title-block <%= tagInfoShow ? "active" : "" %>" role="tab">
								<h3 class="title" data-toggle="collapse" data-target="#tag-info-content" role="button">
									<%= __("Tag") %>
								</h3>
							</div>
							<div id="tag-info-content" class="form-panel-collapse collapse <%= tagInfoShow ? "show" : "" %>" role="tabpanel">
								<div class="form-panel-body">
									<div class="form-group row">
										<div class="col-sm-6">
											<label class="control-label" for="tag"><%= __("Tag") %></label>
											<select class="form-control boxed" name="tag">
												<option value="">- <%= __("None") %> -</option>
												<option value="0" <%= tag === 0 ? "selected" : "" %> style="color: #7B34F7;">(0) Event</option>
												<option value="1" <%= tag === 1 ? "selected" : "" %> style="color: #C60400;">(1) Hot</option>
												<option value="2" <%= tag === 2 ? "selected" : "" %> style="color: #D68600;">(2) New</option>
												<option value="3" <%= tag === 3 ? "selected" : "" %> style="color: #5ACB29;">(3) Sale</option>
											</select>
										</div>
									</div>
									<div class="form-group row">
										<div class="col-sm-6">
											<label class="control-label" for="tagValidAfter"><%= __("Tag valid from") %></label>
											<input type="datetime-local" class="form-control boxed" name="tagValidAfter" value="<%= tagValidAfter ? tagValidAfter.tz(user.tz).format("YYYY-MM-DDTHH:mm") : "" %>">
											<span class="datetime-local-reset"><i class="fa fa-times"></i></span>
										</div>
										<div class="col-sm-6">
											<label class="control-label" for="tagValidBefore"><%= __("Tag valid to") %></label>
											<input type="datetime-local" class="form-control boxed" name="tagValidBefore" value="<%= tagValidBefore ? tagValidBefore.tz(user.tz).format("YYYY-MM-DDTHH:mm") : "" %>">
											<span class="datetime-local-reset"><i class="fa fa-times"></i></span>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div id="discount-info" class="form-panel" role="tablist">
							<div class="form-panel-heading title-block <%= discountInfoShow ? "active" : "" %>" role="tab">
								<h3 class="title" data-toggle="collapse" data-target="#discount-info-content" role="button">
									<%= __("Discount") %>
								</h3>
							</div>
							<div id="discount-info-content" class="form-panel-collapse collapse <%= discountInfoShow ? "show" : "" %>" role="tabpanel">
								<div class="form-panel-body">
									<div class="form-group row">
										<div class="col-sm-6">
											<label class="control-label" for="discount"><%= __("Discount") %> (%)</label>
											<input type="number" class="form-control boxed" name="discount" value="<%= discount %>" min="0" max="100">
										</div>
									</div>
									<div class="form-group row">
										<div class="col-sm-6">
											<label class="control-label" for="discountValidAfter"><%= __("Discount valid from") %></label>
											<input type="datetime-local" class="form-control boxed" name="discountValidAfter" value="<%= discountValidAfter ? discountValidAfter.tz(user.tz).format("YYYY-MM-DDTHH:mm") : "" %>">
											<span class="datetime-local-reset"><i class="fa fa-times"></i></span>
										</div>
										<div class="col-sm-6">
											<label class="control-label" for="discountValidBefore"><%= __("Discount valid to") %></label>
											<input type="datetime-local" class="form-control boxed" name="discountValidBefore" value="<%= discountValidBefore ? discountValidBefore.tz(user.tz).format("YYYY-MM-DDTHH:mm") : "" %>">
											<span class="datetime-local-reset"><i class="fa fa-times"></i></span>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group row"></div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		function onItemAdded() {
			$('input[name^="itemTemplateIds"]').change(function() {
				$(this).closest('.row').find('input[name^="boxItemIds"]').val("");
			});
			$(".remove-item").click(function () {
				$(this).closest(".row").remove();
				addItemIcons();
			});
		}
		onItemAdded();
		$("#add-item").click(function() {
			$("#items").append(
				"<div class='form-group row'>" +
				"	<div class='col-sm-7'>" +
				"		<label class='control-label' for='itemTemplateIds[]'>" +
				"			<%= __('Item template ID') %>" +
				"			<a class='btn btn-secondary btn-sm remove-item' title='' href='#'><i class='fa fa-minus'></i></a>" +
				"		</label>" +
				"		<input type='text' class='form-control boxed' name='itemTemplateIds[]' value=''>" +
				"	</div>" +
				"	<div class='col-sm-2'>" +
				"		<label class='control-label' for='boxItemCounts[]'><%= __('Count') %></label>" +
				"		<input type='number' class='form-control boxed mt-2' name='boxItemCounts[]' value='1' min='1' max='1000000'>" +
				"	</div>" +
				"	<div class='col-sm-3'>" +
				"		<label class='control-label' for='boxItemIds[]'><%= __('Service item ID') %></label>" +
				"		<input type='number' class='form-control boxed mt-2' name='boxItemIds[]' value='' placeholder='<%= __('optional') %>' min='1' max='100000000'>" +
				"	</div>" +
				"</div>"
			);
			onItemAdded()
			addAutocomplete();
		});
		$("#form").validate(config.validations);
	});
</script>
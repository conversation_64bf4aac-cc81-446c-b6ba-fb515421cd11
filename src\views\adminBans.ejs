<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Account Bans") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/bans/add"><%= __("Ban account") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Start time") %></th>
											<th><%= __("End time") %></th>
											<th><%= __("Active") %></th>
											<th><%= __("Description") %></th>
											<th><%= __("IP address") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

bans.forEach(ban => {
	const active = moment(ban.get("startTime")) < moment() && moment(ban.get("endTime")) > moment() && ban.get("active");

	tableData.push([
		ban.get("accountDBID"),
		ban.get("info")?.get("userName") || "-",
		moment(ban.get("startTime")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		moment(ban.get("endTime")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		`<span class="text-${active ? "success" : "danger"}">${active ? __("Yes") : __("No")}</span>`,
		ban.get("description"),
		JSON.parse(ban.get("ip") || "[]").join("<br>") || "-",
		`<a class="btn btn-secondary btn-sm" href="/bans/edit?accountDBID=${ban.get("accountDBID")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/bans/delete?accountDBID=${ban.get("accountDBID")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
{"env": {"es6": true, "node": true}, "extends": ["eslint:recommended"], "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}, "rules": {"no-await-in-loop": "off", "no-shadow": "error", "no-redeclare": "warn", "arrow-spacing": "warn", "init-declarations": ["error", "always"], "no-setter-return": "warn", "no-duplicate-imports": "warn", "no-unused-vars": "off", "no-const-assign": "error", "no-import-assign": "error", "space-unary-ops": ["error", {"words": true, "nonwords": false}], "quote-props": ["error", "as-needed"], "no-unneeded-ternary": "error", "rest-spread-spacing": ["error", "never"], "no-param-reassign": "warn", "no-self-compare": "error", "prefer-object-spread": "warn", "indent": ["warn", "tab", {"SwitchCase": 1}], "quotes": ["error", "double"], "semi": ["error", "always"], "no-lone-blocks": "error", "no-multi-str": "error", "no-path-concat": "error", "no-process-exit": "off", "no-lonely-if": "error", "no-spaced-func": "warn", "no-confusing-arrow": "error", "prefer-template": "error", "no-constant-condition": "error", "arrow-body-style": ["warn", "as-needed"], "no-var": "error", "no-nested-ternary": "warn", "no-multiple-empty-lines": "warn", "no-template-curly-in-string": "error", "no-empty-function": "warn", "no-empty": ["error", {"allowEmptyCatch": true}], "no-floating-decimal": "error", "no-multi-spaces": "warn", "guard-for-in": "error", "yoda": "error", "array-callback-return": "error", "no-dupe-else-if": "error", "vars-on-top": "warn", "func-call-spacing": "warn", "no-eval": "error", "brace-style": ["warn", "1tbs", {"allowSingleLine": true}], "use-isnan": "error", "comma-dangle": ["warn", "never"], "comma-spacing": "warn", "comma-style": "warn", "dot-location": ["error", "property"], "handle-callback-err": "off", "max-nested-callbacks": ["error", {"max": 4}], "max-statements-per-line": ["error", {"max": 4}], "no-console": "off", "no-trailing-spaces": ["warn"], "object-curly-spacing": ["warn", "always"], "array-bracket-spacing": ["warn", "never"], "prefer-const": "error", "space-in-parens": "warn", "space-infix-ops": "warn", "spaced-comment": "warn", "space-before-blocks": "warn", "space-before-function-paren": ["warn", {"anonymous": "never", "named": "never", "asyncArrow": "always"}], "keyword-spacing": ["warn", {"before": true, "after": true}], "key-spacing": "warn"}, "globals": {}}
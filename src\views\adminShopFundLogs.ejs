<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Shop Fund Logs") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="from"><%= __("Period from") %></label>
				<input type="datetime-local" class="form-control boxed" name="from" value="<%= from.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
			</div>
			<div class="form-group">
				<label for="to"><%= __("to") %></label>
				<input type="datetime-local" class="form-control boxed" name="to" value="<%= to.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
			</div>
			<div class="form-group">
				<label for="accountDBID"><%= __("Account ID") %></label>
				<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>">
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Log time") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Amount") %></th>
											<th><%= __("Balance") %></th>
											<th><%= __("Type") %></th>
											<th><%= __("Value") %></th>
											<th><%= __("Info") %></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

logs.forEach(log => {
	const [type, value, info] = log.get("description").split(",");

	let valueLink = value;
	let infoLink = info;

	switch (type) {
		case "Buy":
		case "BuyCancel":
			valueLink = `${__('Product ID')}: <a href="/shop_products/edit?id=${value}">${value}</a>`;
			infoLink = `${__('Log ID')}: <a href="/shop_pay_logs?id=${info}">${info}</a>`;
			break;
		case "PromoCode":
			valueLink = `${__("Function")}: ${valueLink}`;
			infoLink = `${__('Promo code ID')}: <a href="/promocodes/edit?promoCodeId=${info}">${info}</a>`;
			break;
	}

	tableData.push([
		log.get("id"),
		moment(log.get("createdAt")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss"),
		log.get("accountDBID"),
		log.get("account")?.get("userName") || "-",
		log.get("amount") > 0 ? `+${log.get("amount")}` : log.get("amount"),
		log.get("balance"),
		__(type) || "-",
		valueLink || "-",
		infoLink || "-"
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]]
		}));
		$(".section").show();
	});
</script>
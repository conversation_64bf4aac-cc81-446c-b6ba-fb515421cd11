<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge;">
<title>Login</title>
<link rel="stylesheet" href="/public/launcher/css/login.css">
<script type="text/javascript" src="/public/launcher/js/jquery-1.11.1.min.js"></script>
<script>
	var PATCH_URL = "<%= patchUrl %>";
</script>
<script type="text/javascript" src="/public/launcher/js/launcher-util.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher-errors.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher.js"></script>
<script>
	// hCaptcha 回调函数
	function onSignupCaptchaSuccess(token) {
		console.log('hCaptcha 验证成功');
		window.hcaptchaToken = token;
	}

	function onSignupCaptchaExpired() {
		console.log('hCaptcha 验证过期');
		window.hcaptchaToken = null;
	}

	function onSignupCaptchaError(error) {
		console.error('hCaptcha 验证错误:', error);
		window.hcaptchaToken = null;
	}

	function signupAction(login, email, password, password2) {
		if (password != password2) {
			Launcher.showError("<%= __('Two password entries do not match.') %>");
			return;
		}

		var result = LauncherAPI.signupAction(login, email, password, window.hcaptchaToken);

		if (result) {
			if (result.Return) {
				Launcher.goTo("SignupVerifyForm?locale=<%= __req.query.locale %>", true);
			} else {
				switch (result.ReturnCode) {
					case 9:
						Launcher.showError("<%= __('Too many requests.') %>");
						break;
					case 10:
						Launcher.showError("<%= __('This login is already exists.') %>");
						break;
					case 11:
						Launcher.showError("<%= __('Please enter a login containing 3 to 24 Latin letters or numbers.') %>");
						break;
					case 12:
						Launcher.showError("<%= __('Please enter valid email address.') %>");
						break;
					case 13:
						Launcher.showError("<%= __('Please enter a password between 8 and 128 characters long.') %>");
						break;
					case 14:
						Launcher.showError("<%= __('This email address is already exists.') %>");
						break;
					case 15:
						Launcher.showError("<%= __('Please confirm that you are human.') %>");
						break;
					default:
						Launcher.showError(result.ReturnCode + ": " + result.Msg);
						console.log(result);
				}
			}
		}
	}

	$(function() {
		$("#userSignupForm").submit(function() {
			signupAction($('#login').val(), $('#email').val(), $('#password').val(), $('#password2').val());
			return false;
		});

		$("#btnToLogin").click(function() {
			Launcher.goTo("LoginForm?locale=<%= __req.query.locale %>");
		});

		$(window).on("load", function() {
			Launcher.loaded(320, 570);
		});
	});
</script>
</head>
<body oncontextmenu="return false" ondragstart="return false" onselectstart="return false" style="overflow: hidden;">
<div class="wrap">
	<div class="msg-modal" id="msg-modal"><span></span></div>
<%_ if (!captcha) { _%>
	<form class="form-horizontal" name="userSignupForm" method="post" action="" id="userSignupForm" style="padding-top: 20px; display: none;">
<%_ } else { _%>
	<form class="form-horizontal" name="userSignupForm" method="post" action="" id="userSignupForm" style="display: none;">
<%_ } _%>
		<div class="form-group">
			<div class="col">
				<input type="text" class="form-control form-uid" id="login" name="login" value="" placeholder="<%= __('Enter login') %>" maxlength="24" tabindex="1">
			</div>
		</div>
		<div class="form-group">
			<div class="col">
				<input type="text" class="form-control form-email" id="email" name="email" value=""  placeholder="<%= __('Enter email') %>" maxlength="128" tabindex="2">
			</div>
		</div>
		<div class="form-group">
			<div class="col">
				<input type="password" class="form-control form-password" id="password" name="password" value="" placeholder="<%= __('Enter password') %>" maxlength="128" tabindex="2">
			</div>
		</div>
		<div class="form-group">
			<div class="col">
				<input type="password" class="form-control form-password" id="password2" name="password2" value=""  placeholder="<%= __('Confirm password') %>" maxlength="128" tabindex="2">
			</div>
		</div>
<%_ if (captcha) { _%>
		<div class="form-group" style="padding-top: 10px; padding-bottom: 5px;">
			<%- captcha %>
		</div>
<%_ } _%>
		<div class="form-group" style="padding-top: 20px;">
			<div class="col" style="text-align: center;">
				<button type="submit" tabindex="3" class="btn-blue"><%= __('Sign Up') %></button>
				<br>
				<a href="#" style="text-decoration: underline;" id="btnToLogin"><%= __('Login to existing account') %></a>
			</div>
		</div>
	</form>
	<button class="btn-close" onclick="Launcher.sendCommand('close');">Close</button>
</div>
<!--/wrap-->
</body>
</html>
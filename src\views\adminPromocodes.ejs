<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Shop Promo Codes") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/promocodes/add"><%= __("Create new promo code") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Promo code") %></th>
											<th><%= __("Description") %> (<%= locale %>)</th>
											<th><%= __("Assigned function") %></th>
											<th><%= __("Activations") %></th>
											<th><%= __("Valid from") %></th>
											<th><%= __("Valid to") %></th>
											<th><%= __("Active") %></th>
											<th><%= __("Valid") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

promocodes.forEach(promocode => {
	const valid = promocode.get("active") &&
		(promocode.get("maxActivations") === 0 || promocode.get("currentActivations") < promocode.get("maxActivations")) &&
		moment().isSameOrAfter(moment(promocode.get("validAfter"))) &&
		moment().isSameOrBefore(moment(promocode.get("validBefore")));

	tableData.push([
		promocode.get("promoCodeId"),
		`<a href="/promocodes/edit?promoCodeId=${promocode.get("promoCodeId")}">${promocode.get("promoCode")}</a>`,
		promocode.get("strings")[0]?.get("description") || "-",
		promocode.get("function"),
		`<a href="/promocodes_activated?promoCodeId=${promocode.get("promoCodeId")}">${promocode.get("maxActivations") > 0 ? `${promocode.get("currentActivations")}/${promocode.get("maxActivations")}` : promocode.get("currentActivations")}</a>`,
		moment(promocode.get("validAfter")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		moment(promocode.get("validBefore")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		promocode.get("active") ? __("Yes") : __("No"),
		`<span class="text-${valid ? "success" : "danger"}">${valid ? __("Yes") : __("No")}</span>`,
		`<a class="btn btn-secondary btn-sm" href="/promocodes/edit?promoCodeId=${promocode.get("promoCodeId")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/promocodes/delete?promoCodeId=${promocode.get("promoCodeId")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, { className: "text-nowrap text-monospace" }, null, null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
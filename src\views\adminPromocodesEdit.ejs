<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Shop Promo Code") %> ID <%= promoCodeId %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Promo Code Information") %></h3>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="promoCode"><%= __("Promo code") %></label>
								<input type="text" class="form-control boxed text-monospace" name="promoCode" value="<%= promoCode %>" disabled>
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="aFunction"><%= __("Assigned function") %></label>
								<select class="form-control boxed" name="aFunction">
								<%_ promocodeFunctions.forEach(promocodeFunction => { _%>
									<option value="<%= promocodeFunction %>" <%= promocodeFunction == aFunction ? 'selected' : "" %>><%= promocodeFunction %></option>
								<%_ }) _%>
								</select>
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="maxActivations"><%= __("Maximum of activations") %></label>
								<input type="number" class="form-control boxed" name="maxActivations" value="<%= maxActivations %>" min="0" max="100000000">
								<i>0 &mdash; <%= __("no limit on the number of activations.") %></i>
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="validAfter"><%= __("Valid from") %></label>
								<input type="datetime-local" class="form-control boxed" name="validAfter" value="<%= validAfter.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="validBefore"><%= __("Valid to") %></label>
								<input type="datetime-local" class="form-control boxed" name="validBefore" value="<%= validBefore.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
						</div>
						<div class="form-group">
							<label>
								<input type="checkbox" class="checkbox" name="active" <%- active ? "checked=\"checked\"" : "" %>>
								<span><%= __("Active") %></span>
							</label>
						</div>
						<div class="title-block">
							<h3 class="title"><%= __("Promo Code Description") %></h3>
						</div>
						<%_ languages.forEach(language => { _%>
						<div class="form-group row">
							<div class="col-sm-2 mt-2">
								<label for="description[<%= language %>]" class="control-label"><%= __(language) %></label>
							</div>
							<div class="col-sm-10">
								<input type="text" class="form-control boxed" name="description[<%= language %>]" value="<%= description[language] || "" %>" minlength="1" maxlength="2048">
							</div>
						</div>
						<%_ }) _%>
						<div class="form-group row"></div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
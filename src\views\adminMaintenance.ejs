<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Server Maintenance") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/maintenance/add"><%= __("Add new maintenance plan") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Start time") %></th>
											<th><%= __("End time") %></th>
											<th><%= __("Active") %></th>
											<th><%= __("Description") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

maintenances.forEach(maintenance => {
	const active = moment(maintenance.get("startTime")) < moment() && moment(maintenance.get("endTime")) > moment();

	tableData.push([
		maintenance.get("id"),
		moment(maintenance.get("startTime")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		moment(maintenance.get("endTime")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		`<span class="text-${active ? "success" : "danger"}">${active ? __("Yes") : __("No")}</span>`,
		maintenance.get("description") || "-",
		`<a class="btn btn-secondary btn-sm" href="/maintenance/edit?id=${maintenance.get("id")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/maintenance/delete?id=${maintenance.get("id")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
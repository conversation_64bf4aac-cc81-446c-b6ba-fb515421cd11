<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Your Profile") %></h1>
	</div>
	<section class="section">
		<div class="row sameheight-container">
			<div class="col-md-6">
				<div class="card sameheight-item">
					<div class="card-block">
						<div class="card-title-block">
							<h3 class="title"><%= __("Session Information") %></h3>
						</div>
						<section class="mb-2">
							<%= __("User login ID") %>: <strong><%= user.login %></strong>
						</section>
						<section class="mb-2">
							<%= __("User login SN") %>: <strong><%= user.userSn || __("No") %></strong>
						</section>
						<section class="mb-2">
							<%= __("Authentication type") %>: <strong><%= user.type === "steer" ? "STEER" : "QA Login" %></strong>
						</section>
						<section class="mb-2">
							<%= __("Time zone") %>: <strong><%= moment().tz(user.tz).format("Z") %> (<%= user.tz %>)</strong>
						</section>
					</div>
				</div>
			</div>
		</div>
		<%_ if (user.type === "steer") { _%>
		<div class="row sameheight-container">
			<div class="col-md-6">
				<div class="card sameheight-item">
					<div class="card-block">
						<div class="card-title-block">
							<h3 class="title"><%= __("Permitted Functions") %></h3>
						</div>
						<section>
							<table class="table table-hover">
								<thead>
									<tr>
										<th>GUFID</th>
										<th><%= __("Function name") %></th>
									</tr>
								</thead>
								<tbody>
									<%_ Object.keys(user.functions).forEach(id => { _%>
									<tr>
										<td><%= id %></td>
										<td><%= user.functions[id] %></td>
									</tr>
									<%_ }) _%>
								</tbody>
							</table>
						</section>
					</div>
				</div>
			</div>
		</div>
		<%_ } _%>
	</section>
</article>
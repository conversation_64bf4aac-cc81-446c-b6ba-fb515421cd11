<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Server List (SLS)") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/servers/add"><%= __("Add new server") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Name") %></th>
											<th><%= __("Login IP:port") %></th>
											<th><%= __("Language code") %></th>
											<th><%= __("Type") %></th>
											<th><%= __("Permission") %></th>
											<th><%= __("Enabled") %></th>
											<th><%= __("Crowdness") %></th>
											<th><%= __("Available") %></th>
											<th><%= __("Users online") %></th>
											<th><%= __("Users total") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

servers.forEach(server => {
	tableData.push([
		server.get("serverId"),
		`<a href="/servers/edit?serverId=${server.get("serverId")}">${server.get("nameString")}</a>`,
		`${server.get("loginIp")}:${server.get("loginPort")}${isSlsOverridden(config, server.get("serverId")) ? ' <i class="fa fa-refresh text-warning"></i>' : ''}`,
		`(${server.get("language")}) ${__(server.get("language"))}`,
		server.get("isPvE") ? "PvE" : "PvP",
		server.get("permission"),
		`<span class="text-${server.get("isEnabled") ? "success" : "danger"}">${server.get("isEnabled") ? __("Yes") : __("No")}</span>`,
		server.get("isCrowdness") ? __("Yes") : __("No"),
		`<span class="text-${server.get("isAvailable") ? "success" : "danger"}">${server.get("isAvailable") ? __("Yes") : __("No")}</span>`,
		server.get("usersOnline"),
		server.get("usersTotal"),
		`<a class="btn btn-secondary btn-sm" href="/servers/edit?serverId=${server.get("serverId")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/servers/delete?serverId=${server.get("serverId")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Shop Pay Logs") %></h1>
	</div>
	<%- include("partials/adminReportForm", { servers, from, to, serverId, accountDBID }) -%>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Log time") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Server ID") %></th>
											<th><%= __("Product ID") %></th>
											<th><%= __("Box ID") %></th>
											<th><%= __("Price") %></th>
											<th><%= __("Quantity") %></th>
											<th><%= __("Amount") %></th>
											<th><%= __("Status") %></th>
											<th><%= __("Updated") %></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

logs.forEach(log => {
	tableData.push([
		log.get("id"),
		moment(log.get("createdAt")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss"),
		log.get("accountDBID"),
		log.get("account")?.get("userName") || "-",
		`(${log.get("serverId")}) ${log.get("server")?.get("nameString") || ""}`,
		`<a href="/shop_products/edit?id=${log.get("productId")}">${log.get("productId")}</a>`,
		log.get("boxId") || "-",
		log.get("price"),
		log.get("quantity"),
		log.get("price") * log.get("quantity"),
		`<span class="text-${log.get("status") == "completed" ? "success" : (log.get("status") == "deposit" ? "primary" : "danger")}">${__(log.get("status"))}</span>`,
		moment(log.get("updatedAt")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss")
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]]
		}));
		$(".section").show();
	});
</script>
<div class="main hero-unit white-text">
	<h2><%= __("Available Coupons") %></h2>
	<%_ if (coupons.length > 0) { _%>
		<div class="table coupons-list">
		<%_ coupons.forEach(coupon => { _%>
			<div class="coupon-box">
				<div class="coupon" data-coupon="<%= coupon.get("coupon") %>"><span>******<i class="fa fa-eye"></i></span></div>
				<div class="discount"><%= __("Discount: %s%", coupon.get("discount")) %></div>
				<div class="date"><%= __("Available until:") %></div>
				<div class="date"><%= moment.utc(coupon.get("validBefore")).utcOffset(-tzOffset).format("DD.MM.YYYY HH:mm:ss") %></div>
			</div>
		<%_ }) _%>
		</div>
	<%_ } else { _%>
		<hr><%= __("You have no coupons available.") %>
	<%_ } _%>
</div>
<%_ if (couponsActivated.length > 0) { _%>
<div class="main hero-unit white-text left-test">
	<h2><%= __("Used Coupons") %></h2>
	<div class="table coupons-list">
	<%_ couponsActivated.forEach(couponActivated => { _%>
		<div class="coupon-box used">
			<div class="coupon" data-coupon="<%= couponActivated.get("info").get("coupon") %>"><span>******<i class="fa fa-eye"></i></span></div>
			<div class="discount"><%= __("Discount: %s%", couponActivated.get("info").get("discount")) %></div>
			<div class="date"><%= __("Used at:") %></div>
			<div class="date"><%= moment.utc(couponActivated.get("createdAt")).utcOffset(-tzOffset).format("DD.MM.YYYY HH:mm:ss") %></div>
		</div>
	<%_ }) _%>
	</div>
</div>
<%_ } _%>
<script type="text/javascript">
	$(function() {
		$(".coupon").click(function() {
			$(this).html($(this).data("coupon"));
		});
	});
</script>
<%_
const strings = {
	id: __("ID"),
	accountDBID: __("Account ID"),
	serverId: __("Server ID"),
	benefitId: __("Benefit ID"),
	categoryId: __("Category ID"),
	productId: __("Product ID"),
	promoCodeId: __("Promo code ID"),
	language: __("Language code")
};
_%>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Admin Operations Report") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="from"><%= __("Period from") %></label>
				<input type="datetime-local" class="form-control boxed" name="from" value="<%= from.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
			</div>
			<div class="form-group">
				<label for="to"><%= __("to") %></label>
				<input type="datetime-local" class="form-control boxed" name="to" value="<%= to.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
			</div>
			<div class="form-group">
				<label for="userId"><%= __("User login ID") %></label>
				<input type="text" class="form-control boxed" name="userId" value="<%= userId %>">
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<table class="table table-hover data-table-json mt-5">
								<thead>
									<tr>
										<th><%= __("Report time") %></th>
										<th><%= __("User login ID") %></th>
										<th><%= __("Function") %></th>
										<th><%= __("Parameters") %></th>
										<th><%= __("IP address") %></th>
										<th></th>
									</tr>
								</thead>
							</table>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

reports.forEach(report => {
	const [query, body] = JSON.parse(report.get("payload"));
	const parameters = [];

	Object.keys(query).forEach(key => {
		if (query[key] !== "" && !/^from.+/.test(key)) {
			parameters.push(`<span class="text-secondary">${strings[key] || key}</span>: ${query[key]}`)
		}
	});

	tableData.push([
		moment(report.get("reportTime")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss"),
		report.get("userId"),
		`<a href="/operations_report/view?id=${report.get("id")}&amp;from=${from.tz(user.tz).format("YYYY-MM-DDTHH:mm")}&amp;to=${to.tz(user.tz).format("YYYY-MM-DDTHH:mm")}">${report.get("function")}</a>`,
		parameters.join("<br>") || "-",
		report.get("ip"),
		`<a class="btn btn-secondary btn-sm" href="/operations_report/view?id=${report.get("id")}&amp;from=${from.tz(user.tz).format("YYYY-MM-DDTHH:mm")}&amp;to=${to.tz(user.tz).format("YYYY-MM-DDTHH:mm")}"><i class="fa fa-file-text-o"></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Account Benefit") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group">
							<label class="control-label" for="accountDBID"><%= __("Account ID") %></label>
							<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>" disabled>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="benefitId"><%= __("Benefit ID") %></label>
								<select class="form-control boxed" name="benefitIds[]" disabled>
								<%_ Array.from(accountBenefits.getAll()).forEach(({ string, id }) => { _%>
									<option value="<%= id %>" <%= benefitId == id ? 'selected' : "" %>>(<%= id %>) <%= string %></option>
								<%_ }) _%>
								</select>
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="availableUntil"><%= __("Available until") %></label>
								<input type="datetime-local" class="form-control boxed" name="availableUntil" value="<%= availableUntil.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
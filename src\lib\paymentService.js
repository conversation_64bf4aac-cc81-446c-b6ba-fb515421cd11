"use strict";

const EasypayClient = require('easypay-node-sdk');
const uuid = require('uuid').v4;
const crypto = require('crypto');

/**
 * 支付服务类
 */
class PaymentService {
    /**
     * 构造函数
     * @param {object} config 支付配置
     * @param {string} config.domain 易支付API域名
     * @param {number} config.pid 商户ID
     * @param {string} config.key 商户密钥
     */
    constructor(config) {
        this.config = config;
        
        // 创建客户端实例
        try {
            // 如果是默认导出
            if (typeof EasypayClient === 'object' && EasypayClient.default) {
                this.client = new EasypayClient.default({
                    domain: config.domain,
                    pid: config.pid, 
                    key: config.key
                });
            } 
            // 如果是普通导出
            else if (typeof EasypayClient === 'function') {
                this.client = new EasypayClient({
                    domain: config.domain,
                    pid: config.pid, 
                    key: config.key
                });
            }
            // 如果是简单API对象
            else {
                this.client = EasypayClient;
            }
        } catch (error) {
            console.error('初始化支付客户端失败:', error);
            throw error;
        }
    }

    /**
     * 创建支付订单
     * @param {object} orderData 订单数据
     * @param {string} orderData.type 支付类型(alipay|wxpay)
     * @param {string} orderData.out_trade_no 订单号
     * @param {string} orderData.notify_url 异步通知地址
     * @param {string} orderData.return_url 支付成功后的回调地址
     * @param {string} orderData.name 商品名称
     * @param {string|number} orderData.money 金额
     * @returns {Promise<{url: string}>} 支付URL
     */
    async createOrder(orderData) {
        try {
            // 确保所有必要参数都存在
            const payData = {
                pid: this.config.pid,
                ...orderData,
                sitename: orderData.sitename || '游戏商城'
            };
            
            // 先复制数据进行处理
            const data = {...payData};
            
            // 检查并修复URL参数，避免易支付报错
            // 确保notify_url和return_url有值且正确格式化，这是关键参数
            const defaultDomain = 'tera.kvplay.com:8090';
            
            // 检查并修复notify_url
            if (!data.notify_url || data.notify_url.trim() === '') {
                console.error('notify_url为空，使用默认绝对值');
                data.notify_url = 'http://' + defaultDomain + '/shop/PaymentNotify';
            }
            
            // 检查并修复return_url
            if (!data.return_url || data.return_url.trim() === '') {
                console.error('return_url为空，使用默认绝对值');
                data.return_url = 'http://' + defaultDomain + '/shop/PaymentReturn/' + data.out_trade_no;
            }
            

            
            // 检查参数是否包含空格或特殊字符并清理
            const checkUrlParam = (name, value) => {
                if (!value) return;
                if (value.includes(' ') || value.includes('+')) {
                    return value.replace(/\s+/g, '').replace(/\+/g, '%2B');
                }
                return value;
            };
            
            data.notify_url = checkUrlParam('notify_url', data.notify_url) || data.notify_url;
            data.return_url = checkUrlParam('return_url', data.return_url) || data.return_url;
            
            // 按键名升序排序，构建签名字符串 (签名时不能编码参数)
            const sortedKeys = Object.keys(data).sort();
            let signStr = '';
            
            for (const key of sortedKeys) {
                // 跳过空值
                if (data[key] === '' || data[key] === undefined || data[key] === null) {
                    continue;
                }
                signStr += `${key}=${data[key]}&`;
            }
            
            // 计算签名
            const finalSignStr = signStr.substring(0, signStr.length - 1) + this.config.key;
            const sign = crypto.createHash('md5').update(finalSignStr).digest('hex');
            
            // 确保域名末尾没有斜杠
            const domainWithoutSlash = this.config.domain.replace(/\/+$/, '');
            
            // 构建URL参数 - 使用原始值，不进行额外编码
            let urlParams = '';
            urlParams += 'pid=' + this.config.pid;
            urlParams += '&type=' + data.type;
            urlParams += '&out_trade_no=' + data.out_trade_no;
            urlParams += '&notify_url=' + data.notify_url;
            urlParams += '&return_url=' + data.return_url;
            urlParams += '&name=' + data.name;
            urlParams += '&money=' + data.money;
            urlParams += '&sitename=' + data.sitename;
            urlParams += '&sign=' + sign;
            urlParams += '&sign_type=MD5';
            
            const url = domainWithoutSlash + '/submit.php?' + urlParams;
            

            
            // 返回URL
            return { url: url };
        } catch (error) {
            console.error('支付订单创建失败:', error);
            throw error;
        }
    }

    /**
     * 验证支付异步通知
     * @param {object} notifyData 通知数据
     * @returns {boolean} 是否验证通过
     */
    verifyNotify(notifyData) {
        try {
            // 优先使用SDK的verify方法(如果存在)
            if (typeof this.client.verify === 'function') {
                return this.client.verify(notifyData);
            }
            

            
            // 复制一个对象进行处理
            const data = {...notifyData};
            
            // 获取并移除签名
            const sign = data.sign;
            const signType = data.sign_type || 'MD5';
            
            if (!sign) {
                console.error('通知数据中没有签名字段');
                return false;
            }
            
            // 删除sign和sign_type字段，它们不参与签名
            delete data.sign;
            delete data.sign_type;
            
            // 按键名升序排序
            const sortedKeys = Object.keys(data).sort();
            
            // 构建签名字符串
            let signString = '';
            for (const key of sortedKeys) {
                // 跳过空值
                if (data[key] === '' || data[key] === undefined || data[key] === null) {
                    continue;
                }
                signString += `${key}=${data[key]}&`;
            }
            
            // 添加密钥
            signString = signString.substring(0, signString.length - 1) + this.config.key;
            
            // 计算签名
            const md5Hash = crypto.createHash('md5').update(signString).digest('hex');
            
            // 比较签名
            return md5Hash === sign;
        } catch (error) {
            console.error('验证支付通知失败:', error);
            return false;
        }
    }
    
    /**
     * 生成唯一订单号
     * @returns {string} 订单号
     */
    generateOrderNo() {
        return `PAY${Date.now()}${uuid().substring(0, 8)}`;
    }
}

module.exports = PaymentService; 
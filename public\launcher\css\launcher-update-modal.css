/* 启动器更新模态窗口样式 */
.update-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.8);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 10000;
}

.update-modal-content {
	background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
	border-radius: 10px;
	box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
	width: 450px;
	max-width: 90%;
	color: white;
	overflow: hidden;
}

.update-header {
	padding: 20px;
	background: rgba(0, 0, 0, 0.2);
	position: relative;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.update-header h2 {
	margin: 0;
	font-size: 18px;
	font-weight: bold;
	color: white;
}

.update-header .close-btn {
	position: absolute;
	top: 15px;
	right: 15px;
	width: 25px;
	height: 25px;
	background: rgba(255, 255, 255, 0.1);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	font-size: 16px;
	transition: background 0.3s;
}

.update-header .close-btn:hover {
	background: rgba(255, 255, 255, 0.2);
}

.update-body {
	padding: 20px;
}

.version-info {
	margin-bottom: 20px;
}

.version-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 8px;
	font-size: 14px;
}

.version-label {
	color: rgba(255, 255, 255, 0.8);
}

.changelog-section {
	margin-bottom: 20px;
}

.changelog-section h3 {
	margin: 0 0 10px 0;
	font-size: 16px;
	color: white;
}

.changelog-content {
	background: rgba(0, 0, 0, 0.2);
	padding: 15px;
	border-radius: 5px;
	font-size: 13px;
	line-height: 1.5;
	max-height: 120px;
	overflow-y: auto;
	color: rgba(255, 255, 255, 0.9);
}

.update-progress {
	margin-top: 20px;
}

.progress-bar {
	width: 100%;
	height: 20px;
	background: rgba(0, 0, 0, 0.3);
	border-radius: 10px;
	overflow: hidden;
	margin-bottom: 10px;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #4caf50, #45a049);
	width: 0%;
	transition: width 0.3s ease;
}

.progress-text {
	text-align: center;
	font-size: 14px;
	color: rgba(255, 255, 255, 0.8);
}

.update-footer {
	padding: 20px;
	background: rgba(0, 0, 0, 0.2);
	border-top: 1px solid rgba(255, 255, 255, 0.1);
	display: flex;
	justify-content: space-between;
	gap: 10px;
}

.btn-update-now,
.btn-update-later {
	padding: 10px 20px;
	border: none;
	border-radius: 5px;
	cursor: pointer;
	font-size: 14px;
	font-weight: bold;
	transition: all 0.3s ease;
	flex: 1;
}

.btn-update-now {
	background: linear-gradient(135deg, #4caf50, #45a049);
	color: white;
}

.btn-update-now:hover {
	background: linear-gradient(135deg, #45a049, #3d8b40);
	transform: translateY(-2px);
}

.btn-update-later {
	background: rgba(255, 255, 255, 0.1);
	color: white;
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-update-later:hover {
	background: rgba(255, 255, 255, 0.2);
}

.btn-update-now:disabled {
	background: rgba(255, 255, 255, 0.1);
	cursor: not-allowed;
	transform: none;
} 
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Cheats Report") %></h1>
	</div>
	<%- include("partials/adminReportForm", { servers, from, to, serverId, accountDBID }) -%>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Report time") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Character") %></th>
											<th><%= __("Type") %></th>
											<th><%= __("Skill") %></th>
											<th><%= __("Hits") %>/<%= __("Limit") %></th>
											<th><%= __("Target") %></th>
											<th><%= __("Client IP") %></th>
											<th><%= __("Server ID") %></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

reports.forEach(report => {
	const [account, name, hits, limit, skillId, zoneId, x, y, z, huntingZoneId, templateId] = report.get("cheatInfo").split(",");

	const dungeon = datasheetModel.strSheetDungeon.get(locale)?.getOne(huntingZoneId);
	const creature = datasheetModel.strSheetCreature.get(locale)?.getOne(huntingZoneId, templateId);

	let type = __("Other");

	switch (report.get("type")) {
		case 0:
			type = "PPS (packets/sec)";
			break;
		case 1:
			type = "Memeslash";
			break;
	}

	tableData.push([
		moment(report.get("reportTime")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		report.get("accountDBID"),
		report.get("account")?.get("userName") || "-",
		name,
		type,
		skillId,
		`${hits}/${limit}`,
		`(${huntingZoneId}) ${dungeon?.string || huntingZoneId}<br>(${templateId}) ${creature?.name || templateId}<br>${x}, ${y}, ${z}`,
		report.get("ip"),
		`(${report.get("serverId")}) ${report.get("server")?.get("nameString") || ""}`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]]
		}));
		$(".section").show();
	});
</script>
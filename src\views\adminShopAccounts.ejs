<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Shop Accounts") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/shop_accounts/add"><%= __("Create new account") %></a>
	</section>
	<section class="section">
		<div class="alert alert-secondary">
			<%= __("Accounts are created automatically when funds are deposited to the user's Shop balance.") %>
		</div>
	</section>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="accountDBID"><%= __("Account ID") %></label>
				<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>">
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Balance") %></th>
											<th><%= __("Discount") %></th>
											<th><%= __("Active") %></th>
											<th><%= __("Created") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

accounts.forEach(account => {
	tableData.push([
		account.get("accountDBID"),
		account.get("info")?.get("userName") || "-",
		account.get("balance"),
		`${account.get("discount")}%`,
		account.get("active") ? __("Yes") : __("No"),
		moment(account.get("createdAt")).tz(user.tz).format("YYYY-MM-DD HH:mm"),
		`<a class="btn btn-secondary btn-sm" href="/shop_accounts/edit?accountDBID=${account.get("accountDBID")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/shop_accounts/delete?accountDBID=${account.get("accountDBID")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
{"err_1044481": "STEER connection error.", "err_1044482": "Can't register STEER.", "err_1044483": "STEER not registered.", "err_1044484": "STEER read data error.", "err_1044485": "STEER write data error.", "err_16778215": "STEER session expired. Please log in again.", "err_16778216": "Cannot connect to STEER system. Please check server status.", "err_16778217": "Obstacle occurred during communication. Please check server status.", "err_16778218": "You have no available authority. Please contact administrator.", "err_16778219": "Unknown error. Please contact administrator.", "err_16778220": "You have no authority. Please contact administrator.", "err_16778221": "Invalid localizing data. Please contact administrator.", "err_16778222": "Invalid data. Please contact administrator.", "err_16778223": "Exceptional situation occurred. Please contact administrator.", "err_33554436": "Server is not connected.", "err_33554437": "Exceeded data transfer time limit", "err_83885830": "Database error occurred.", "err_83886080": "Processed normally.", "err_83886180": "ID or password does not match.", "err_83886280": "You have no authority or function is not configured for execution type", "err_83886281": "User group is inert", "err_83886282": "Function group is inert", "err_83886283": "Function is inert", "err_83886284": "Target group does not have authority for performing function or has problem in function.", "err_83886285": "Function call type is not defined", "err_83886286": "You have no authority or function is not configured for cast type", "err_83886287": "Cast status is either changed or does not belong to cast target group", "err_83886288": "Cast status is either changed or cast does not exist", "err_83886289": "Does not belong to inspect group", "err_83886290": "Target data does not exist", "err_83886291": "Duplicated log in. Previous log in session is terminated.", "err_83886292": "Session expired.", "err_83886293": "Client IP changed. Please log in again.", "err_83886294": "Account already exists.", "err_83886295": "User cannot log in.", "err_167771910": "Database error occurred.", "Invalid QA login or password.": "Invalid QA login or password.", "The session has expired.": "The session has expired.", "Function access denied": "Function access denied", "Operation failed": "Operation failed", "The field must contain the value as a number.": "The field must contain the value as a number.", "The field contains an existing server ID.": "The field contains an existing server ID.", "The field must contain a valid IP value.": "The field must contain a valid IP value.", "The field must contain a valid port value.": "The field must contain a valid port value.", "The field must be a valid value.": "The field must be a valid value.", "The field contains already existing language code.": "The field contains already existing language code.", "The field must be between 1 and 256 characters.": "The field must be between 1 and 256 characters.", "The field has invalid value.": "The field has invalid value.", "Sync server state": "Sync server state", "Sync server state with the PlanetDB database after saving": "Sync server state with the PlanetDB database after saving", "Sync server state with the PlanetDB database is not configured.": "Sync server state with the PlanetDB database is not configured.", "The field must be between 1 and 1024 characters.": "The field must be between 1 and 1024 characters.", "The field must be between 1 and 50 characters.": "The field must be between 1 and 50 characters.", "The field must be between 1 and 2048 characters.": "The field must be between 1 and 2048 characters.", "The field must contain a valid date.": "The field must contain a valid date.", "The field contains a date that is too late.": "The field contains a date that is too late.", "The field must be between 3 and 24 characters.": "The field must be between 3 and 24 characters.", "The field must be between 8 and 128 characters.": "The field must be between 8 and 128 characters.", "The field must contain a valid email.": "The field must contain a valid email.", "The field must contain a valid number.": "The field must contain a valid number.", "Added benefit already exists.": "Added benefit already exists.", "The field contains existing benefit ID on account.": "The field contains existing benefit ID on account.", "The field contains invalid function.": "The field contains invalid function.", "The field contains not existing promo code ID.": "The field contains not existing promo code ID.", "The field contains inactive promo code ID.": "The field contains inactive promo code ID.", "The field contains expired promo code ID.": "The field contains expired promo code ID.", "The field contains the promo code ID with the activation limit reached.": "The field contains the promo code ID with the activation limit reached.", "The field must be between 6 and 20 characters.": "The field must be between 6 and 20 characters.", "The field must be between 3 and 8 characters.": "The field must be between 3 and 8 characters.", "The field contains not existing account ID.": "The field contains not existing account ID.", "The field contains an existing promo code.": "The field contains an existing promo code.", "The field contains an existing coupon.": "The field contains an existing coupon.", "Shop account with specified account ID already exists.": "Shop account with specified account ID already exists.", "This promo code has already been activated on the specified account ID.": "This promo code has already been activated on the specified account ID.", "The field must be between 1 and 255 characters.": "The field must be between 1 and 255 characters.", "No items have been added to the product.": "No items have been added to the product.", "A non-existent item has been added": "A non-existent item has been added", "Added item already exists.": "Added item already exists.", "The field contains already banned account ID.": "The field contains already banned account ID.", "There are no Service Items for the specified service item IDs.": "There are no Service Items for the specified service item IDs.", "No items have been added to the box.": "No items have been added to the box.", "The field contains not existing character ID.": "The field contains not existing character ID.", "The field contains not existing server ID.": "The field contains not existing server ID.", "Task with this box ID is already running. Check tasks queue.": "Task with this box ID is already running. Check tasks queue.", "The field contains already existing name.": "The field contains already existing name.", "The field contains already existing email.": "The field contains already existing email.", "The field must contain an existing category ID.": "The field must contain an existing category ID.", "Profile": "Profile", "Logout": "Logout", "Dashboard": "Dashboard", "Maintenance": "Maintenance", "Servers": "Servers", "Servers List (SLS)": "Servers List (SLS)", "Strings": "Strings", "Accounts": "Accounts", "Benefits": "Benefits", "Characters": "Characters", "Reports": "Reports", "Game Reports": "Game Reports", "Activity": "Activity", "Cheats": "Cheats", "Chronoscrolls": "Chronoscrolls", "Shop": "Shop", "Categories": "Categories", "Products": "Products", "Shop Logs": "Shop Logs", "Fund Logs": "Fund Logs", "Pay Logs": "Pay Logs", "Promo Codes": "Promo Codes", "Coupons": "Coupons", "List": "List", "Activated": "Activated", "API Settings": "API Settings", "Yes": "Yes", "No": "No", "Confirm": "Confirm", "unconfigured": "unconfigured", "hidden": "hidden", "optional": "optional", "Confirm Deletion": "Confirm Deletion", "Are you sure you want to delete this entries?": "Are you sure you want to delete this entries?", "No data available in table": "No data available in table", "Showing _START_ to _END_ of _TOTAL_ entries": "Showing _START_ to _END_ of _TOTAL_ entries", "Showing 0 to 0 of 0 entries": "Showing 0 to 0 of 0 entries", "(filtered from _MAX_ total entries)": "(filtered from _MAX_ total entries)", "Show _MENU_ entries": "Show _MENU_ entries", "Loading...": "Loading...", "Filter": "Filter", "No matching records found": "No matching records found", "First": "First", "Last": "Last", "Next": "Next", "Previous": "Previous", "activate to sort column ascending": "activate to sort column ascending", "activate to sort column descending": "activate to sort column descending", "Please enter login": "Please enter login", "Please enter password": "Please enter password", "Your Profile": "Your Profile", "Session Information": "Session Information", "Permitted Functions": "Permitted Functions", "User login ID": "User login ID", "User login SN": "User login SN", "Function name": "Function name", "Authentication type": "Authentication type", "Time zone": "Time zone", "Login ID": "Login ID", "Password": "Password", "Save account information": "Save account information", "Login": "<PERSON><PERSON>", "Show full report": "Show full report", "Show full log": "Show full log", "Servers Stats": "Servers Stats", "Under maintenance": "Under maintenance", "Server Maintenance": "Server Maintenance", "Add new maintenance plan": "Add new maintenance plan", "ID": "ID", "Start time": "Start time", "End time": "End time", "Description": "Description", "Add Server Maintenance Plan": "Add Server Maintenance Plan", "Back to list": "Back to list", "Add": "Add", "Edit Server Maintenance Plan": "Edit Server Maintenance Plan", "Save": "Save", "Server List (SLS)": "Server List (SLS)", "Add new server": "Add new server", "Login IP:port": "Login IP:port", "Name": "Name", "Language": "Language", "Type": "Type", "Enabled": "Enabled", "Crowdness": "Crowdness", "Available": "Available", "Users online": "Users online", "Users total": "Users total", "Add Server": "Add Server", "Server ID": "Server ID", "Language code": "Language code", "Login IP": "Login IP", "Login port": "Login port", "Name string": "Name string", "Description string": "Description string", "Threshold low": "Threshold low", "Threshold medium": "Threshold medium", "Only PvE": "Only PvE", "Is crowdness": "Is crowdness", "Is available": "Is available", "Is enabled": "Is enabled", "Edit Server": "Edit Server", "Server List Strings": "Server List Strings", "Add new strings": "Add new strings", "Game language": "Game language", "Category PvE": "Category PvE", "Category PvP": "Category PvP", "Server offline": "Server offline", "Server low": "Server low", "Server medium": "Server medium", "Server high": "Server high", "Crowdness no": "Crowdness no", "Crowdness yes": "Crowdness yes", "Popup": "Popup", "Add Server Strings": "Add Server Strings", "Edit Server Strings": "Edit Server Strings", "Create new account": "Create new account", "User name": "User name", "Email": "Email", "Registered": "Registered", "Permission": "Permission", "Privilege": "Privilege", "Last login": "Last login", "Last server": "Last server", "Last IP": "Last IP", "QA": "QA", "GM": "GM", "MT": "MT", "PL": "PL", "Banned": "Banned", "Add Account": "Add Account", "Account Information": "Account Information", "Account Benefits": "Account Benefits", "Benefit ID": "Benefit ID", "None": "None", "Available until": "Available until", "Add benefit": "Add benefit", "Edit Account": "Edit Account", "Ban account": "Ban account", "Quick Actions": "Quick Actions", "Send Box": "Send Box", "Add Benefit": "Add Benefit", "Add Product": "Add Product", "Add Category": "Add Category", "Start Maintenance": "Start Maintenance", "Reset": "Reset", "Set GM": "Set GM", "Set QA": "Set QA", "Set MT": "Set MT", "Set PL": "Set PL", "Account Benefits List": "Account Benefits List", "Account ID": "Account ID", "Show": "Show", "Add new benefit": "Add new benefit", "Please enter account ID.": "Please enter account ID.", "Benefit description": "Benefit description", "Add Account Benefit": "Add Account Benefit", "Edit Account Benefit": "Edit Account Benefit", "Male": "Male", "Female": "Female", "Human": "Human", "High Elf": "High Elf", "Aman": "<PERSON><PERSON>", "Castanic": "<PERSON><PERSON><PERSON>", "Popori": "Pop<PERSON>", "Baraka": "<PERSON><PERSON>", "Elin": "<PERSON><PERSON>", "Warrior": "Warrior", "Lancer": "Lancer", "Slayer": "Slayer", "Berserker": "<PERSON><PERSON><PERSON><PERSON>", "Sorcerer": "Sorcerer", "Archer": "<PERSON>", "Priest": "Priest", "Mystic": "Mystic", "Reaper": "Reaper", "Gunner": "<PERSON>", "Brawler": "Brawler", "Ninja": "Ninja", "Valkyrie": "Valkyrie", "Common": "Common", "Uncommon": "Uncommon", "Rare": "Rare", "Superior": "Superior", "Mythic": "Mythic", "Account Characters List": "Account Characters List", "Please enter account ID and select the server.": "Please enter account ID and select the server.", "Level": "Level", "Race": "Race", "Gender": "Gender", "Class": "Class", "Activity Report": "Activity Report", "Period from": "Period from", "to": "to", "All": "All", "Report time": "Report time", "Action (playtime)": "Action (playtime)", "Client IP": "Client IP", "Create": "Create", "Edit": "Edit", "Delete": "Delete", "Characters Report": "Characters Report", "Action": "Action", "Cheats Report": "Cheats Report", "Cheat info": "Cheat info", "Character": "Character", "Skill": "Skill", "Hits": "Hits", "Limit": "Limit", "Target": "Target", "Location": "Location", "Other": "Other", "Chronoscrolls Report": "Chronoscrolls Report", "Chrono ID": "Chrono ID", "Info": "Info", "Shop Accounts": "Shop Accounts", "Accounts are created automatically when funds are deposited to the user's Shop balance.": "Accounts are created automatically when funds are deposited to the user's Shop balance.", "If additional information is not set, the information for the product will be taken from the first item added.": "If additional information is not set, the information for the product will be taken from the first item added.", "In the Last login field, the date of the last entry into the game is specified. Box will not be sent to users who logged into the game before this date.": "In the Last login field, the date of the last entry into the game is specified. Box will not be sent to users who logged into the game before this date.", "Balance": "Balance", "Discount": "Discount", "Active": "Active", "Valid": "<PERSON><PERSON>", "Activations": "Activations", "No change": "No change", "Inactive": "Inactive", "Enter game": "Enter game", "Leave game": "Leave game", "count": "count", "example": "example", "Shop Categories": "Shop Categories", "Add new category": "Add new category", "Sort": "Sort", "Sort.": "Sort.", "Title": "Title", "Add Shop Cetegory": "Add Shop Cetegory", "Category Title": "Category Title", "en": "English", "ru": "Russian", "cn": "Chinese", "fr": "French", "de": "German", "jp": "Japanese", "kr": "Korean", "se": "Swedish", "th": "Thai", "tw": "Chinese (Taiwan)", "us": "English (USA)", "Edit Shop Category": "Edit Shop Category", "Slides": "Slides", "Shop Slides": "Shop Slides", "Add new slide": "Add new slide", "Prio.": "Prio.", "Priority": "Priority", "Product title": "Product title", "Display start date": "Display start date", "Display end date": "Display end date", "Background image": "Background image", "Preview": "Preview", "Upload new": "Upload new", "Required resolution: %sx%sx": "Required resolution: %sx%sx", "not used": "not used", "File upload": "File upload", "Remove image": "Remove image", "Background image file not selected.": "Background image file not selected.", "The resolution must be: %sx%s": "The resolution must be: %sx%s", "Only JPG and PNG files are allowed": "Only JPG and PNG files are allowed", "The file is too big! Max size: %s MB": "The file is too big! Max size: %s MB", "File too large": "File too large", "This image is used in the slide.": "This image is used in the slide.", "Error deleting image.": "Error deleting image.", "Add Shop Slide": "Add Shop Slide", "Edit Shop Slide": "Edit Shop Slide", "Shop Pay Logs": "Shop Pay Logs", "Shop Fund Logs": "Shop Fund Logs", "Log time": "Log time", "Quantity": "Quantity", "Amount": "Amount", "Operation description": "Operation description", "Product ID": "Product ID", "Box ID": "Box ID", "Price": "Price", "Tag": "Tag", "Status": "Status", "Updated": "Updated", "Shop Promo Codes": "Shop Promo Codes", "Create new promo code": "Create new promo code", "Promo code": "Promo code", "Assigned function": "Assigned function", "Valid from": "<PERSON>id from", "Valid to": "Valid to", "Tag valid from": "Tag valid from", "Tag valid to": "Tag valid to", "Discount valid from": "Discount valid from", "Discount valid to": "Discount valid to", "Maximum of activations": "Maximum of activations", "no limit on the number of activations.": "no limit on the number of activations.", "Add Shop Promo Code": "Add Shop Promo Code", "Promo Code Information": "Promo Code Information", "Promo Code Description": "Promo Code Description", "Edit Shop Promo Code": "Edit Shop Promo Code", "Shop Activated Promo Codes": "Shop Activated Promo Codes", "Promo code ID": "Promo code ID", "Activate promo code": "Activate promo code", "Please enter account ID or promo code ID.": "Please enter account ID or promo code ID.", "Activation time": "Activation time", "Activate Promo Code": "Activate Promo Code", "Activate": "Activate", "Shop Coupons": "Shop Coupons", "Add Shop Coupon": "Add Shop Coupon", "Edit Shop Coupon": "Edit Shop Coupon", "Coupon Information": "Coupon Information", "Shop Activated Coupons": "Shop Activated Coupons", "Create new coupon": "Create new coupon", "Coupon": "Coupon", "Coupon ID": "Coupon ID", "Please enter account ID or coupon ID.": "Please enter account ID or coupon ID.", "Configuration Variables": "Configuration Variables", "Parameter": "Parameter", "Value": "Value", "Add Shop Account": "Add Shop Account", "Edit Shop Account": "Edit Shop Account", "Shop Products": "Shop Products", "Category": "Category", "Category ID": "Category ID", "Items count": "Items count", "Icon": "Icon", "Published": "Published", "Add new product": "Add new product", "Add Shop Product": "Add Shop Product", "Edit Shop Product": "Edit Shop Product", "Product Information": "Product Information", "Product Items": "Product Items", "Add item": "Add item", "Tag Information": "Tag Information", "Discount Information": "Discount Information", "Additional Information": "Additional Information", "Rare grade": "Rare grade", "Item count": "Item count", "Item ID": "Item ID", "Item": "<PERSON><PERSON>", "Service item ID": "Service item ID", "Count": "Count", "Validate form": "Validate form", "Item template ID": "Item template ID", "Registered Users": "Registered Users", "Online Users": "Online Users", "completed": "Completed", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "rejected": "Rejected", "PromoCode": "Promo code", "ChronoScroll": "Chronoscroll", "ShopApi": "Shop API", "SignUp": "Sign Up", "AccountDeletion": "Account deletion", "BalanceChange": "Balance change", "Buy": "Buy", "BuyCancel": "Buy cancel", "Bans": "Bans", "Account Bans": "Account <PERSON>s", "Edit Ban Account": "Edit Ban Account", "Ban Account": "Ban Account", "Kick all online players": "Kick all online players", "Operations": "Operations", "Gateway API": "Gateway API", "Admin Operations Report": "Admin Operations Report", "Gateway API Report": "Gateway API Report", "Function": "Function", "Endpoint": "Endpoint", "IP address": "IP address", "View Operations Report": "View Operations Report", "View Gateway API Report": "View Gateway API Report", "Parameters": "Parameters", "Operation Parameters": "Operation Parameters", "Request Parameters": "Request Parameters", "Item Claim": "<PERSON><PERSON>", "Send": "Send", "Send Online": "Send Online", "Send All": "Send All", "Tasks Queue": "Tasks Queue", "Queue": "Queue", "Items (Box)": "Items (Box)", "Item Claim Boxes": "<PERSON><PERSON>", "Add new box": "Add new box", "Days": "Days", "Add Item Claim Box": "Add <PERSON>em <PERSON>", "Box Information": "Box Information", "Box Items": "Box Items", "Edit Item Claim Box": "Edit <PERSON><PERSON>", "Items": "Items", "Admin Tasks Queue": "Admin Tasks Queue", "Admin Tasks Queue Log": "Admin Tasks Queue Log", "Restart tasks queue": "Restart tasks queue", "Cancel all tasks": "Cancel all tasks", "Cancel failed tasks": "Cancel failed tasks", "Show tasks log": "Show tasks log", "Message": "Message", "Created": "Created", "Pending": "Pending", "Failed": "Failed", "Cancelled": "Cancelled", "Success": "Success", "Time": "Time", "Task ID": "Task ID", "Task name": "Task name", "Task tag": "Task tag", "Send Item Claim Box": "Send <PERSON>em <PERSON> Box", "Recipient Information": "Recipient Information", "Character ID": "Character ID", "Send Item Claim Box (All Users)": "Send Item Claim Box (All Users)", "Results of Sending Item Claim Box": "Results of Sending <PERSON><PERSON>", "Show tasks": "Show tasks", "Operation has errors during execution": "Operation has errors during execution", "Operation completed successfully.": "Operation completed successfully.", "Operation is running in the background...": "Operation is running in the background...", "Rejected with message": "Rejected with message", "Boxes": "Boxes", "Log": "Log", "Logs": "Logs", "Item Claim Boxes Logs": "<PERSON><PERSON>es Logs", "Show all tasks": "Show all tasks", "Log ID": "Log ID", "Box": "Box", "Chronoscroll": "Chronoscroll", "[unknown]": "[unknown]", "With selected": "With selected", "d.": "d.", "hr.": "hr.", "min.": "min.", "sec.": "sec.", "Change password": "Change password", "The API settings contain a dynamic overriding of the server information.": "The API settings contain a dynamic overriding of the server information.", "Launcher": "Launcher", "Launcher Logs": "Launcher Logs", "Version": "Version", "Crash game": "Crash game", "Exit game": "Exit game", "Sign in": "Sign in", "Start download": "Start download", "Finish download": "Finish download", "Downloading": "Downloading", "Versions Information": "Versions Information", "Node.js Version": "Node.js Version", "TERA API Version": "TERA API Version", "DB Version": "DB Version"}
﻿@charset "utf-8";
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1.2;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after, q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
* {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
}
body {
	background-image: url(../images/bg.jpg);
	background-repeat: no-repeat;
	background-position: 0px 80px;
	background-color: transparent;
	font-size: 12px;
	-webkit-app-region: drag;
}
body, button {
	font-family: Helvetica, Arial, sans-serif;
}
button {
	outline: none;
	-webkit-app-region: no-drag;
}
a {
	text-decoration: none;
	color: #fff;
	-webkit-app-region: no-drag;
}
.wrap {
	background-image: url(../images/bg.png);
	background-repeat: no-repeat;
	background-position: 0px 0px;
	width: 960px;
	height: 610px;
	padding-top: 11px; /* 保持向下移动10px的修改 */
	position: relative;
	cursor: default;
	-webkit-app-region: drag;
}
.msg-modal {
	display: none;
	position: absolute;
	right: 121px;
	top: 110px;
	padding: 12px;
	width: 500px;
	font-size: 14px;
	line-height: 16px;
	color: #fff;
	border-radius: 3px;
	z-index: 9999;
	box-shadow: 0 1px 5px rgba(0,0,0,0.9);
	text-shadow: 0 0 1px rgba(0,0,0,0.5);
	cursor: default;
	opacity: 0.95;
}
.msg-modal .close-btn {
	position: absolute;
	top: 2px;
	right: 3px;
	width: 18px;
	height: 18px;
	line-height: 17px;
	text-align: center;
	cursor: pointer;
}
.msg-modal h1 {
	font-size: 16px;
	font-weight: bold;
	line-height: 16px;
	margin-bottom: 5px;
}
.msg-modal.success {
	background: rgba(0, 153, 0, 0.15);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(0, 187, 0, 0.3);
}
.msg-modal.error {
	background: rgba(204, 51, 51, 0.15);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(204, 102, 102, 0.3);
}
.header {
	margin-top: 100px;
	margin-left: 20px;
	margin-bottom: 10px;
	float: left;
}
.header h3 {
	color: #8ea8df;
	font-size: 18px;
	margin-left: 10px;
	margin-top: 15px;
	margin-bottom: 7px;
}
.content {
	clear: both;
	float: left;
	margin-left: 20px;
}
.logo {
	float: left;
}
.col {
	float: left;
	margin-top: -2px;
	margin-left: 25px;
	height: 75px;
}
.ico-wrap {
	float: left;
	width: 344px;
	margin-bottom: 10px;
}
.ico-wrap li {
	float: left;
}
.ico-wrap li a, .ico-wrap li span {
	background-image: url(../images/btn-ico.png);
	background-repeat: no-repeat;
	display: block;
	text-indent: -9999px;
}
.ico-wrap li a {
	height: 30px;
	width: 30px;
	margin-top: 6px;
}
.ico-wrap li.ico-fb a {
	background-position: 0 -20px;
	margin-right: 10px;
}
.ico-wrap li.ico-gamer a {
	background-position: -50px -20px;
}
.ico-wrap li.ico-vip {
	float: right;
	margin-top: -14px;
}
.ico-wrap li.ico-vip span {
	height: 50px;
	width: 110px;
	background-position: -100px 0px;
}
.nav {
	color: #ffffff !important;
	margin-bottom: 10px;
	font-size: 14px;
}
.nav li {
	float: left;
	margin-right: 6px;
}
.nav li span {
	padding: .26em;
	display: block;
}
.nav li a {
	color: #767c99;
	padding: .26em;
	display: block;
}
.nav li a:hover {
	color: #ffffff;
}
.notice {
	width: 400px;
	margin-left: 25px;
	margin-bottom: 14px;
	height: 125px;
}
.notice h3 {
	color: #767c99;
	font-size: 18px;
	padding-left: 30px;
	background-image: url(../images/ico.png);
	background-repeat: no-repeat;
	background-position: 0px 2px;
	margin-bottom: 7px;
}
.notice h3 span {
	font-weight: bold;
}
.notice li {
	font-family: Helvetica, Arial, sans-serif;
	border-color: #4d152f;
	border-style: solid;
	border-bottom-width: 1px;
	height: 24px;
	line-height: 24px;
}
.notice li a {
	color: #bbbbbb;
	display: block;
}
.notice li a:hover {
	color: #ffd74d;
}
.notice li a span.time {
	float: right;
}
.ad {
	width: 500px;
	height: 162px;
	margin-bottom: 14px;
}
.ad h3 {
	display: none;
}
.progress {
	margin-left: 15px;
	margin-right: 20px;
	color: #767c99;
	line-height: 22px;
	width: 439px;
	float: left;
	overflow-x: hidden;
	white-space: nowrap;
}
.progress h3 {
	display: none;
}
.progress>div {
	margin-bottom: 10px;
	height: 22px;
}
.progress .tit {
	float: left;
	margin-right: 9px;
	text-align: right;
	width: 40px;
}
.progress .progress-bar {
	float: left;
	background-color: #1e1f45;
	color: #fff;
	width: 390px;
	height: 22px;
	position: relative;
}
.progress .progress-bar-complete {
	background-color: #68317e;
	height: 22px;
	position: absolute;
	z-index: 1;
	top: 0px;
	left: 0px;
}
.progress .file-name {
	position: absolute;
	z-index: 2;
	top: 0xp;
	left: .5em;
}
.check {
	float: left;
	margin-left: -5px;
	margin-right: 10px;
	width: 72px;
	height: 60px;
}
.check .btn-check {
	width: 50px;
	height: 50px;
	border-width: 0;
	font-size: 11px;
	margin-top: 2px;
	cursor: pointer;
	text-decoration: -9999px;
	background-image: url(../images/repair-btn.png);
	background-repeat: no-repeat;
	text-indent: -9999px;
	background-color: transparent;
	background-position: 0px 0px;
}
.check .btn-check:hover {
	background-position: 0px -50px;
}

/* 修复按钮悬浮提示样式 */
.check .btn-check[data-tooltip] {
	position: relative;
}
.check .btn-check[data-tooltip]:hover::after {
	content: attr(data-tooltip);
	position: absolute;
	bottom: 60px;
	left: 50%;
	transform: translateX(-50%);
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
	color: #fff;
	padding: 10px 20px;
	border-radius: 8px;
	font-size: 12px;
	z-index: 1000;
	text-indent: 0;
	text-decoration: none;
	line-height: 1.3;
	width: 320px;
	text-align: center;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	animation: tooltip-fade-in 0.3s ease-out;
}
.check .btn-check[data-tooltip]:hover::before {
	content: "";
	position: absolute;
	bottom: 52px;
	left: 50%;
	transform: translateX(-50%);
	width: 0;
	height: 0;
	border-left: 8px solid transparent;
	border-right: 8px solid transparent;
	border-top: 8px solid rgba(255, 255, 255, 0.15);
	z-index: 1000;
}

@keyframes tooltip-fade-in {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(5px);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}
.bottom {
	width: 840px;
	display: block;
}
.gamestart {
	float: left;
	margin-left: 5px;
	margin-top: -16px;
}
.gamestart .btn {
	cursor: pointer;
	width: 240px;
	height: 90px;
	color: #fff;
	font-size: 24px;
	font-weight: bold;
	border-width: 0;
	line-height: 0;
	display: block;
	padding: 0;
	text-decoration: -9999px;
	background-image: url(../images/btn-game-start.png);
	background-repeat: no-repeat;
	background-color: transparent;
	background-position: 0px 1px;
	text-shadow: 1px 1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, -1px -1px 0 #000, 1px 0px 0 #000, 
		0px 1px 0 #000, -1px 0px 0 #000, 0px -1px 0 #000, 1px 1px 0px rgba(0,0,0,0);
}
.gamestart .btn:hover {
	background-position: 0px -99px;
}
.gamestart .btn-gamestart {
	text-transform: uppercase;
	background-image: url(../images/btn-gs-orange.png);
}
.gamestart .btn-wrong {
	background-image: url(../images/btn-gs-red.png);
}
.gamestart .btn-wait {
	background-image: url(../images/btn-gs-blue.png);
}
.gamestart .btn-update {
	background-image: url(../images/btn-gs-green.png);
}
.gamestart .btn-break {
	background-image: url(../images/btn-gs-blue.png);
}
.gamestart .btn-repair {
	background-image: url(../images/btn-gs-blue.png);
}

/* DPS插件按钮样式 */
.dps-plugin {
	float: left;
	margin-left: 50px;  /* 调整适配新的gamestart位置 (5px + (240px - 150px)/2 = 50px，但稍微左移) */
	margin-top: -50px;  /* 保持原有的上移距离 */
	margin-bottom: 15px;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
}

.dps-plugin .btn-dps {
	cursor: pointer;
	width: 150px;  /* ← 更窄 */
	height: 36px;  /* ← 更短 */
	color: #fff;
	font-size: 14px;
	font-weight: bold;
	border: 1px solid rgba(255, 255, 255, 0.3);
	border-radius: 10px;
	background: rgba(72, 239, 139, 0.15);
	backdrop-filter: blur(8px);
	-webkit-backdrop-filter: blur(8px);
	text-shadow: 0 1px 2px rgba(0,0,0,0.4);
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;
	display: block;
}

.dps-plugin .btn-dps:hover {
	background: rgba(72, 239, 139, 0.25);
	transform: translateY(-2px);
	box-shadow: 0 6px 10px rgba(0,0,0,0.2);
}

.dps-plugin .btn-dps:active {
	transform: translateY(0);
	box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.footer {
	font-size: 11px;
	color: #767c99;
	clear: both;
	position: relative;
	top: -10px;
	margin-left: 30px;
}
.btn-close {
	cursor: pointer;
	width: 22px;
	height: 22px;
	border-width: 0;
	background-image: url(../images/btn-close.png);
	background-position: 0px 0px;
	position: absolute;
	top: 90px;
	right: 132px;
	text-indent: -9999px;
	background-color: transparent;
	-webkit-app-region: no-drag;
}
.btn-close:hover {
	background-position: 0px -22px;
}
.btn-logout {
	cursor: pointer;
	width: 18px;
	height: 18px;
	border-width: 0;
	background-image: url(../images/btn-logout.png);
	background-position: 0px 0px;
	position: absolute;
	text-indent: -9999px;
	margin-left: 5px;
	margin-top: 2px;
	background-color: transparent;
}
.btn-logout:hover {
	background-position: 0px -18px;
}
#qaBox {
	position: absolute;
	margin-top: 100px;
	background: #ededed;
	border: 1px solid #000;
	width: 320px;
	height: 137px;
	padding: 0 10px 20px 10px;
	line-height: 30px;
}
#qaBox fieldset {
	border: 1px solid #000;
	padding-bottom: 5px;
}
#qaBox table {
	margin: 0 10px;
	border: 0;
	width: 100%;
	height: 70px;
	vertical-align: middle;
}
#qaBox .qa-hide {
	color: #87759e;
	font-size: 11px;
}
#qaBox #qaModeCheck {
	position: absolute;
	margin-top: -11px;
}
"use strict";

const { verify } = require("hcaptcha");

/**
 * hCaptcha 验证码工具类
 * 用于替换原有的滑块验证码系统
 */
class HCaptcha {
    constructor(secretKey, siteKey) {
        this.secretKey = secretKey;
        this.siteKey = siteKey;
    }

    /**
     * 渲染 hCaptcha HTML 组件
     * @param {string} id - 容器ID
     * @param {Object} options - 选项
     * @returns {string} HTML 字符串
     */
    render(id, options = {}) {
        const config = {
            sitekey: this.siteKey,
            theme: options.theme || "light",
            size: options.size || "normal",
            tabindex: options.tabindex || 0,
            callback: options.callback || "onHcaptchaSuccess",
            "expired-callback": options.expiredCallback || "onHcaptchaExpired",
            "error-callback": options.errorCallback || "onHcaptchaError",
            ...options
        };

        const dataAttributes = Object.keys(config)
            .map(key => `data-${key}="${config[key]}"`)
            .join(" ");

        return `
            <div id="${id}" class="h-captcha" ${dataAttributes}></div>
            <script src="https://js.hcaptcha.com/1/api.js" async defer></script>
            <script>
                window.onHcaptchaSuccess = function(token) {
                    // 验证成功回调
                    console.log('hCaptcha success:', token);
                    if (typeof ${options.callback || 'onHcaptchaSuccess'} === 'function') {
                        ${options.callback || 'onHcaptchaSuccess'}(token);
                    }
                };
                
                window.onHcaptchaExpired = function() {
                    // 验证过期回调
                    console.log('hCaptcha expired');
                    if (typeof ${options.expiredCallback || 'onHcaptchaExpired'} === 'function') {
                        ${options.expiredCallback || 'onHcaptchaExpired'}();
                    }
                };
                
                window.onHcaptchaError = function(error) {
                    // 验证错误回调
                    console.error('hCaptcha error:', error);
                    if (typeof ${options.errorCallback || 'onHcaptchaError'} === 'function') {
                        ${options.errorCallback || 'onHcaptchaError'}(error);
                    }
                };
            </script>
        `;
    }

    /**
     * 验证用户提交的 hCaptcha token
     * @param {string} token - 用户提交的 token
     * @param {string} remoteip - 用户IP地址（可选）
     * @returns {Promise<boolean>} 验证结果
     */
    async verify(token, remoteip = null) {
        try {
            const result = await verify(this.secretKey, token, remoteip);
            return result.success === true;
        } catch (error) {
            console.error('hCaptcha verification error:', error);
            return false;
        }
    }

    /**
     * 验证用户提交的 hCaptcha token（详细结果）
     * @param {string} token - 用户提交的 token
     * @param {string} remoteip - 用户IP地址（可选）
     * @returns {Promise<Object>} 详细验证结果
     */
    async verifyDetailed(token, remoteip = null) {
        try {
            const result = await verify(this.secretKey, token, remoteip);
            return result;
        } catch (error) {
            console.error('hCaptcha verification error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

module.exports = HCaptcha; 
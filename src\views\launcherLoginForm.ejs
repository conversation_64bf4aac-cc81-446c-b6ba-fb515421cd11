<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge;">
<title>Login</title>
<link rel="stylesheet" href="/public/launcher/css/login.css">
<script type="text/javascript" src="/public/launcher/js/jquery-1.11.1.min.js"></script>
<script>
	var PATCH_URL = "<%= patchUrl %>";
</script>
<script type="text/javascript" src="/public/launcher/js/launcher-util.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher-errors.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher.js"></script>
<script>
	function authByLoginAndPassword(login, password) {
		var result = LauncherAPI.loginAction(login, password);

		if (result) {
			if (result.Return) {
				Launcher.goTo("Main", true);
			} else {
				switch (result.ReturnCode) {
					case 9:
						Launcher.showError("<%= __('Too many requests.') %>");
						break;
					case 10:
						Launcher.showError("<%= __('Please enter your login.') %>");
						break;
					case 11:
						Launcher.showError("<%= __('Please enter your password.') %>");
						break;
					case 12:
						Launcher.showError("<%= __('Invalid login or password.') %>");
						break;
					default:
						Launcher.showError(result.ReturnCode + ": " + result.Msg);
						console.log(result);
				}
			}
		}
	}

	$(function() {
		if (!getUrlParam("locale")) {
			return Launcher.goTo("LoginForm?locale=" + locale);
		}

		$("#userLoginForm").submit(function() {
			authByLoginAndPassword($('#login').val(), $('#password').val());
			return false;
		});

		$("#btnJoin").click(function() {
			Launcher.goTo("SignupForm?locale=" + locale);
		});

		$("#btnResetPass").click(function() {
			Launcher.goTo("ResetPasswordForm?locale=" + locale);
		});

		$(window).on("load", function() {
			Launcher.loaded(320, 500);
<%_ if (isPasswordChanged) { _%>
			Launcher.showSuccess("<%= __('Your password has been successfully changed.') %>");
<%_} _%>
		});
	});
</script>
</head>
<body oncontextmenu="return false" ondragstart="return false" onselectstart="return false" style="overflow: hidden;">
<div class="wrap">
	<div class="msg-modal" id="msg-modal"><span></span></div>
<%_ if (!isRegistrationDisabled) { _%>
	<form class="form-horizontal" name="userLoginForm" method="post" action="" id="userLoginForm" style="padding-top: 15px; display: none;">
<%_ } else { _%>
	<form class="form-horizontal" name="userLoginForm" method="post" action="" id="userLoginForm" style="padding-top: 45px; display: none;">
<%_} _%>
		<div class="form-group">
			<div class="col">
				<input type="text" class="form-control form-uid" id="login" name="login" placeholder="<%= __('Login') %>" maxlength="24" tabindex="1">
			</div>
		</div>
		<div class="form-group">
			<div class="col">
				<input type="password" class="form-control form-password" id="password" name="password" placeholder="<%= __('Password') %>" maxlength="128" tabindex="2">
			</div>
		</div>
<%_ if (isEmailVerifyEnabled) { _%>
		<div class="form-group" style="padding-top: 5px;">
			<div class="col">
				<a href="#" style="text-decoration: underline;" id="btnResetPass"><%= __('Forgot your password?') %></a>
			</div>
		</div>
<%_ } _%>
		<div class="form-group" style="padding-top: 25px;">
			<div class="col" style="text-align: center;">
				<button type="submit" tabindex="3" class="btn-orange"><%= __('Sign In') %></button>
<%_ if (!isRegistrationDisabled) { _%>
				<button type="button" tabindex="3" class="btn-blue" id="btnJoin"><%= __('Sign Up') %></button>
<%_ } _%>
			</div>
		</div>
	</form>
	<button class="btn-close" onclick="Launcher.sendCommand('close');">Close</button>
</div>
<!--/wrap-->
</body>
</html>
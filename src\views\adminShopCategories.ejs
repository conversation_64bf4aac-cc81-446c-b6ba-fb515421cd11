<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Shop Categories") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/shop_categories/add"><%= __("Add new category") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Title") %> (<%= locale %>)</th>
											<th><%= __("Active") %></th>
											<th><%= __("Published") %></th>
											<th><%= __("Sort.") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

categories.forEach(category => {
	tableData.push([
		category.get("id"),
		category.get("strings")[0]?.get("title") || "-",
		category.get("active") ? __("Yes") : __("No"),
		`<span class="text-${category.get("active") ? "success" : "danger"}">${category.get("active") ? __("Yes") : __("No")}</span>`,
		category.get("sort"),
		`<a class="btn btn-secondary btn-sm" href="/shop_categories/edit?id=${category.get("id")}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/shop_categories/delete?id=${category.get("id")}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[4, "desc"]],
			columnDefs: [{ orderable: false, targets: -1 }],
			columns: [null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
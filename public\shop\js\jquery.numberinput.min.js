/*! jQuery Number Input v1.1.2 | MIT License | (c) 2024 Non-commercial Project "HSDN" */
!function(r){r.fn.numberInput=function(){return this.each(function(){var a=r(this),n=parseFloat(a.attr("step"))||1,i=parseFloat(a.attr("min")),t=parseFloat(a.attr("max")),d=r('<div class="number-input"></div>');a.wrap(d);var o=r('<div class="custom-arrows"><div class="arrow-up">&#9650;</div><div class="arrow-down">&#9660;</div></div>');a.after(o);var c=function(){var r=a.prop("disabled");o.find(".arrow-up, .arrow-down").toggleClass("disabled",r)};o.find(".arrow-up").click(function(){a.prop("disabled")||a.val(function(r,a){var i=+a+n;return void 0!==t&&i>t&&(i=t),i}).trigger("change")}),o.find(".arrow-down").click(function(){a.prop("disabled")||a.val(function(r,a){var t=+a-n;return void 0!==i&&t<i&&(t=i),t}).trigger("change")}),c(),a.on("change disabledChange",c)})}}(window.jQuery);
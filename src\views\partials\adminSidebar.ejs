<%_
	function filterMenu(items) {
		const result = [];

		items.forEach(item => {
			if (item.page && user.type === "steer" && !Object.values(user.functions).includes(item.page)) {
				return;
			}

			const fields = {
				icon: item.icon,
				name: item.name,
				page: item.page
			};

			if (item.elements) {
				const elements = filterMenu(item.elements);

				if (elements.length > 0) {
					result.push({ ...fields, elements });
				}
			} else {
				result.push(fields);
			}
		});

		return result;
	}

	const menuContents = [
		{ icon: "fa-home", name: __("Dashboard"), page: "/home" },
		{ icon: "fa-sitemap", name: __("Online Users"), page: "/online" },
		{ icon: "fa-wrench", name: __("Maintenance"), page: "/maintenance" },
		{ icon: "fa-desktop", name: __("Servers"), elements: [
			{ name: __("Servers List (SLS)"), page: "/servers" },
			{ name: __("Strings"), page: "/server_strings" }
		] },
		{ icon: "fa-users", name: __("Accounts"), elements: [
			{ name: __("Accounts"), page: "/accounts" },
			{ name: __("Benefits"), page: "/benefits" },
			{ name: __("Characters"), page: "/characters" },
			{ name: __("Bans"), page: "/bans" }
		] },
		{ icon: "fa-rocket", name: __("Launcher"), elements: [
			{ name: __("Log"), page: "/launcher_logs" }
		] },
		{ icon: "fa-shopping-cart", name: __("Shop"), elements: [
			{ name: __("Products"), page: "/shop_products" },
			{ name: __("Categories"), page: "/shop_categories" },
			{ name: __("Slides"), page: "/shop_slides" },
			{ name: __("Accounts"), page: "/shop_accounts" }
		] },
		{ icon: "fa-suitcase", name: __("Shop Logs"), elements: [
			{ name: __("Fund Logs"), page: "/shop_fund_logs" },
			{ name: __("Pay Logs"), page: "/shop_pay_logs" }
		] },
		{ icon: "fa-gift", name: __("Coupons"), elements: [
			{ name: __("Coupons"), page: "/coupons" },
			{ name: __("Activated"), page: "/coupons_activated" }
		] },
		{ icon: "fa-ticket", name: __("Promo Codes"), elements: [
			{ name: __("Promo Codes"), page: "/promocodes" },
			{ name: __("Activated"), page: "/promocodes_activated" }
		] },
		{ icon: "fa-truck", name: __("Item Claim"), elements: [
			{ name: __("Boxes"), page: "/boxes" },
			{ name: __("Log"), page: "/boxes_logs" }
		] },
		{ icon: "fa-book", name: __("Game Reports"), elements: [
			{ name: __("Activity"), page: "/report_activity" },
			{ name: __("Characters"), page: "/report_characters" },
			{ name: __("Cheats"), page: "/report_cheats" },
			{ name: __("Chronoscrolls"), page: "/report_chronoscrolls" }
		] },
		{ icon: "fa-file-text", name: __("Logs"), elements: [
			{ name: __("Operations"), page: "/operations_report" },
			{ name: __("Gateway API"), page: "/gateway_report" },
		] },
		{ icon: "fa-tasks", name: __("Tasks Queue"), elements: [
			{ name: __("Queue"), page: "/tasks" },
			{ name: __("Log"), page: "/tasks_logs" },
		] },
		{ icon: "fa-gear", name: __("API Settings"), page: "/settings" } // <span class="label label-primary">10</span>
	];

	__modules.pluginsLoader.loadComponent("views.adminPanel.adminSidebar.menuContents", menuContents, __);

	const menu = filterMenu(menuContents);
	const page = `/${__endpoint.split("/")[1]}`;
_%>
<aside class="sidebar">
	<div class="sidebar-container">
		<div class="sidebar-header">
			<div class="brand">
				<b>TERA API</b> Admin Panel
			</div>
		</div>
		<nav class="menu">
			<ul class="sidebar-menu metismenu" id="sidebar-menu">
				<%_ menu.forEach(root => { _%>
				<%_ if (root.elements && root.elements.length > 0) { _%>
				<li class="<%= root.elements.find(e => e.page === page) ? "active open" : "" %>">
					<a href=""><i class="fa <%= root.icon %>"></i> <%= root.name %> <i class="fa arrow"></i></a>
					<ul class="sidebar-nav">
					<%_ root.elements.forEach(element => { _%>
						<li class="<%= element.page === page ? "active" : "" %>"><a href="<%= element.page %>"><%= element.name %></a></li>
					<%_ }) _%>
					</ul>
				</li>
				<%_ } else { _%>
				<li class="<%= root.page === page ? "active" : "" %>"><a href="<%= root.page %>"><i class="fa <%= root.icon %>"></i> <%= root.name %></a></li>
				<%_ } _%>
				<%_ }) _%>
			</ul>
		</nav>
	</div>
</aside>
<div class="sidebar-overlay" id="sidebar-overlay"></div>
<div class="sidebar-mobile-menu-handle" id="sidebar-mobile-menu-handle"></div>
<div class="mobile-menu-handle"></div>
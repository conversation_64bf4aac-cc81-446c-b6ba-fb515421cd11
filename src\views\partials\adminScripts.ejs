<script>
	var dataTablesSettings = {
		autoWidth: false,
		pageLength: 25,
		stateSave: true,
		language: {
			decimal: "",
			emptyTable: "<%= __('No data available in table') %>",
			info: "<%= __('Showing _START_ to _END_ of _TOTAL_ entries') %>",
			infoEmpty: "<%= __('Showing 0 to 0 of 0 entries') %>",
			infoFiltered: "<%= __('(filtered from _MAX_ total entries)') %>",
			infoPostFix: "",
			thousands: ",",
			lengthMenu: "<%= __('Show _MENU_ entries') %>",
			loadingRecords: "<%= __('Loading...') %>",
			processing: "",
			search: "<%= __('Filter') %>",
			zeroRecords: "<%= __('No matching records found') %>",
			paginate: {
				first: "<%= __('First') %>",
				last: "<%= __('Last') %>",
				next: "<%= __('Next') %>",
				previous: "<%= __('Previous') %>"
			},
			aria: {
				sortAscending: ": <%= __('activate to sort column ascending') %>",
				sortDescending: ": <%= __('activate to sort column descending') %>"
			}
		},
		drawCallback: function(){
			$("img").each(function() {
				if ($(this).data("src")) {
					$(this).attr("src", $(this).data("src"));
				}
			});
		},
		fnInitComplete: function() {
			$(".dataTables_length select, .dataTables_filter input").each(function() {
				$(this).addClass("boxed");
			});
		}
	};
</script>
<script src="/static/js/vendor.js"></script>
<%_ if (locale !== "en") { _%>
<script src="/static/js/vendor/jquery-validation/localization/messages_<%= locale %>.min.js"></script>
<%_ } _%>
<script src="/static/js/app.js"></script>
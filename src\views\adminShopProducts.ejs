<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Shop Products") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="categoryId"><%= __("Category ID") %></label>
				<select class="form-control boxed change-submit" name="categoryId">
					<option value="">- <%= __("All") %> -</option>
				<%_ categories.forEach(category => { _%>
					<option value="<%= category.get("id") %>" <%= categoryId == category.get("id") ? 'selected' : "" %>>(<%= category.get("id") %>) <%= category.get("strings")[0]?.get("title") || "-" %></option>
				<%_ }) _%>
				</select>
			</div>
		</form>
	</section>
	<section class="section">
		<a class="btn btn-success" title="" href="/shop_products/add?categoryId=<%= categoryId %>&amp;fromCategoryId=<%= categoryId %>"><%= __("Add new product") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th></th>
											<th><%= __("ID") %></th>
											<th></th>
											<th><%= __("Title") %></th>
											<th><%= __("Category ID") %></th>
											<th><%= __("Price") %></th>
											<th><%= __("Discount") %></th>
											<th><%= __("Active") %></th>
											<th><%= __("Published") %></th>
											<th><%= __("Sort.") %></th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

products.forEach((product, id) => {
	tableData.push([
		id,
		id,
		'<div class="item-icon-block">' +
			`<img data-src="/static/images/tera-icons/${product.icon}.png" class="item-icon-grade-${product.rareGrade} item-icon">` +
			`<img data-src="/static/images/icons/icon_grade_${product.rareGrade}.png" class="item-icon-grade" alt="">` +
			(product.tag !== null ? `<img data-src="/static/images/icons/icon_tag_${product.tag}.png" class="item-icon-tag" alt="">` : "") +
			'</div>',
		`<a class="item-grade-${product.rareGrade}" href="/shop_products/edit?id=${id}&amp;fromCategoryId=${categoryId || ""}">${product.title || __("[unknown]")}</a>`,
		`(${product.categoryId}) <a href="/shop_products?categoryId=${product.categoryId}">${product.categoryTitle}</a>`,
		product.price,
		`${product.discount}%`,
		product.active ? __("Yes") : __("No"),
		`<span class="text-${product.published ? "success" : "danger"}">${product.published ? __("Yes") : __("No")}</span>`,
		product.sort,
		`<a class="btn btn-secondary btn-sm" href="/shop_products/edit?id=${id}&amp;fromCategoryId=${categoryId || ""}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/shop_products/delete?id=${id}&amp;categoryId=${product.categoryId}&amp;fromCategoryId=${categoryId || ""}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		var table = $(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			dom: "<'row mb-2'<'col-12 text-right'B>>" +
				"<'row'<'col-sm-6'l><'col-sm-6'f>>" +
				"<'row'<'col-sm-12'tr>>" +
				"<'row'<'col-sm-2 dt-action'><'col-sm-5'i><'col-sm-5'p>>",
			data: serializedTable,
			order: [[9, "desc"]],
			columnDefs: [
				{
					targets: 0,
					render: function(data, type, row, meta) {
						if(type === "display") {
							data = '<label><input type="checkbox" class="dt-checkboxes checkbox"><span></span></label>';
						}
						return data;
					},
					checkboxes: {
						stateSave: false,
						selectAllPages: false,
						selectAllRender: '<label><input type="checkbox" class="dt-checkboxes checkbox"><span></span></label>',
						selectCallback: function() {
							$("#action-form select option:first").html(
								"<%= __("With selected") %> (" + table.column(0).checkboxes.selected().length + ")..."
							);
						}
					}
				},
				{ orderable: false, targets: [2, -1] },
			],
			columns: [null, null, { width: "1%" }, null, null, null, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".dt-action").html(
			'<form id="action-form" method="POST">' +
			'	<select class="custom-select custom-select-sm form-control form-control-sm boxed mt-2">' +
			'		<option value=""><%= __("With selected") %> (0)...</option>' +
			'		<option value="/shop_products/edit/all?fromCategoryId=<%= categoryId %>"><%= __("Edit") %></option>' +
			'		<option value="/shop_products/delete/all?fromCategoryId=<%= categoryId %>"><%= __("Delete") %></option>' +
			'	</select>' +
			'</form>'
		);
		$("#action-form").on("submit", function() {
			$.each(table.column(0).checkboxes.selected(), function(index, rowId) {
				$("#action-form").append(
					$("<input>").attr("type", "hidden").attr("name", "id[]").val(rowId)
				);
			});
		});
		$("#action-form select").change(function() {
			if (table.column(0).checkboxes.selected().length !== 0 && $(this).val()) {
				$(this).closest("form").attr("action", $(this).val());
				if ($(this).val() == "/shop_products/delete/all?fromCategoryId=<%= categoryId %>") {
					$("#confirm-del-modal").modal("show", $(this).closest("form"));
				} else {
					$(this).closest("form").submit();
				}
			} else {
				$("#action-form select option:first").prop("selected", true)
			}
		});
		$(".section").show();
	});
</script>
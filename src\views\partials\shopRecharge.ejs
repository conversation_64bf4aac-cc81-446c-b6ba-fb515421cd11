<style>
    /* 
      Final Modal Solution:
      - The modal container (#paymentModal) is position:fixed to cover the entire viewport, creating a true modal experience.
      - The content (.modal-content-final) is positioned relative to the viewport, but calculated to be centered inside the right-hand content pane.
    */
    
    .modal-content-final {
        background-color: #2a2e35;
        color: #ccc;
        padding: 10px;
        border: 1px solid #888;
        border-radius: 8px;
        text-align: left;
        font-family: Arial, sans-serif;
        
        /* --- Precise Centering Logic --- */
        position: absolute;
        width: 400px;
        top: 50%;
        
        /* The right pane starts at 25% and is 75% wide. Its center is at 25 + (75/2) = 62.5% */
        left: 62.5%; 
        
        /* Pull the box back by half its own width and height to align its center with the target point */
        margin-top: -180px;  /* Approximate half of the modal's height */
        margin-left: -200px; /* Half of the modal's width */
    }
    
    .modal-close-button-final {
        color: #aaa;
        position: absolute;
        top: 5px;
        right: 15px;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }
    
    .modal-close-button-final:hover,
    .modal-close-button-final:focus {
        color: #fff;
        text-decoration: none;
    }
    </style>
    
    <div style="padding: 20px; background: #1a1d22; color: #ccc; font-family: Arial, sans-serif;">
        <h2 style="color: #fff; margin-bottom: 20px;">账户充值</h2>
        
        <div style="background: #2a2e35; padding: 20px; margin-bottom: 20px; border-radius: 8px;">
            <h3 style="color: #fff; margin-bottom: 15px;">选择充值金额</h3>
            <div style="text-align: center;">
                <div class="amount-option" data-amount="10" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; min-width: 100px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold;">¥10</div>
                    <div style="font-size: 12px; color: #aaa;">获得10点数</div>
                    </div>
                <div class="amount-option" data-amount="50" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; min-width: 100px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold;">¥50</div>
                    <div style="font-size: 12px; color: #aaa;">获得50点数</div>
                </div>
                <div class="amount-option" data-amount="100" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; min-width: 100px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold;">¥100</div>
                    <div style="font-size: 12px; color: #aaa;">获得100点数</div>
                </div>
                <div class="amount-option" data-amount="200" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; min-width: 100px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold;">¥200</div>
                    <div style="font-size: 12px; color: #aaa;">获得200点数</div>
                </div>
                <div class="amount-option" data-amount="500" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; min-width: 100px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold;">¥500</div>
                    <div style="font-size: 12px; color: #aaa;">获得500点数</div>
                </div>
                <div class="amount-option" data-amount="1000" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; min-width: 100px; text-align: center;">
                    <div style="font-size: 18px; font-weight: bold;">¥1000</div>
                    <div style="font-size: 12px; color: #aaa;">获得1000点数</div>
                </div>
            </div>
        </div>
            
        <div style="background: #2a2e35; padding: 20px; margin-bottom: 20px; border-radius: 8px;">
            <h3 style="color: #fff; margin-bottom: 15px;">选择支付方式</h3>
            <div style="text-align: center;">
                <div class="payment-method" data-method="alipay" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; width: 150px; text-align: center;">
                    <div style="font-size: 16px;">支付宝</div>
                </div>
                <div class="payment-method" data-method="wxpay" style="display: inline-block; margin: 8px; padding: 15px; background: #38404a; border: 2px solid transparent; border-radius: 8px; cursor: pointer; width: 150px; text-align: center;">
                    <div style="font-size: 16px;">微信支付</div>
                </div>
                </div>
            </div>
    
        <div style="background: #2a2e35; padding: 20px; margin-bottom: 20px; border-radius: 8px;">
            <div style="overflow: hidden; margin-bottom: 10px;">
                <span style="float: left; color: #aaa;">选择金额:</span>
                <span style="float: right; color: #fff; font-weight: bold;">¥<span id="selectedAmount">0</span></span>
            </div>
            <div style="overflow: hidden; margin-bottom: 10px;">
                <span style="float: left; color: #aaa;">获得点数:</span>
                <span style="float: right; color: #fff; font-weight: bold;"><span id="bonusPoints">0</span> 点</span>
            </div>
            <div style="overflow: hidden; border-top: 1px solid #444; padding-top: 10px; margin-top: 10px;">
                <span style="float: left; color: #aaa; font-size: 18px;">总计:</span>
                <span style="float: right; color: #fff; font-weight: bold; font-size: 18px;">¥<span id="totalAmount">0</span></span>
            </div>
        </div>
        
        <div style="text-align: center; margin-bottom: 20px;">
            <button id="submitRecharge" style="background: #13426D; color: #fff; padding: 12px 30px; border: none; border-radius: 6px; font-size: 16px; cursor: pointer; min-width: 150px;">
                确认充值
            </button>
            </div>
            
        <div id="debug-info" style="background: #333; color: #fff; padding: 10px; border-radius: 5px; display: none; margin-top: 20px;"></div>
    </div>
    
    <!-- The Final Modal: Self-contained and correctly positioned -->
    <div id="paymentModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 1000; background-color: rgba(0,0,0,0.6);">
        <div class="modal-content-final">
            <span class="modal-close-button-final">&times;</span>
            <div id="modal-payment-details" style="margin-bottom: 15px; text-align: center;">
                <p style="margin: 5px 0;">订单号: <span id="modal-order-id" style="font-weight: bold; color: #fff;"></span></p>
                <p style="margin: 5px 0;">支付金额: <span id="modal-payment-amount" style="font-weight: bold; color: #d9534f; font-size: 1.2em;"></span> 元</p>
            </div>
            <p id="modal-payment-hint" style="margin: 10px 0; font-size: 1.1em; color: #1b5d9a; text-align: center;"></p>
            <div id="modal-qrcode-image" style="width: 200px; height: 200px; margin: 10px auto; border: 1px solid #ddd; padding: 5px; background: #fff;"></div>
            <p id="modal-payment-expires" style="color: #aaa; font-size: 12px; margin-top: 15px; text-align: center;"></p>
            <p style="color: #aaa; font-size: 12px; margin-top: 5px; text-align: center;">支付完成后，点数将自动充值到您的账户。</p>
        </div>
    </div>
    
    <script>
    $(function() {
        var selectedAmount = null;
        var selectedPaymentMethod = "alipay";
        var paymentCheckInterval = null; // 用于存放定时器的变量
        
        // 金额选择
        $(".amount-option").click(function() {
            $(".amount-option").css("border-color", "transparent");
            $(this).css("border-color", "#1b5d9a");
            selectedAmount = $(this).data("amount");
            updateSummary();
        });
        
        // 支付方式选择
        $(".payment-method").click(function() {
            $(".payment-method").css("border-color", "transparent");
            $(this).css("border-color", "#1b5d9a");
            selectedPaymentMethod = $(this).data("method");
        });
        
        // 更新摘要
            function updateSummary() {
                if (selectedAmount) {
                    $("#selectedAmount").text(selectedAmount);
                    $("#bonusPoints").text(selectedAmount * 1);
                    $("#totalAmount").text(selectedAmount);
                }
            }
            
        // 提交充值
        $("#submitRecharge").click(function() {
            if (!selectedAmount) {
                alert("请选择充值金额");
                return;
            }
            
            var $btn = $(this);
            $btn.prop('disabled', true).text('处理中...');
            $("#debug-info").show().html('<div style="color:blue">正在创建支付订单...</div>');
            
            $.ajax({
                url: '/shop/CreatePaymentOrder',
                method: 'POST',
                data: JSON.stringify({
                    amount: selectedAmount,
                    paymentMethod: selectedPaymentMethod
                }),
                contentType: 'application/json',
                success: function(result) {
                    if (result.Return && result.paymentInfo) {
                        var info = result.paymentInfo;
                        var orderNo = info.orderId || 'N/A';
                        $("#debug-info").hide();
                        
                        // Populate modal with payment info
                        $("#modal-order-id").text(orderNo);
                        $("#modal-payment-amount").text(info.amount || '0.00');
                        $("#modal-payment-hint").text(info.paymentHint || '请使用手机App扫描二维码');
                        $("#modal-qrcode-image").html('<img src="' + info.qrCodeUrl + '" style="width: 100%; height: 100%;" alt="支付二维码">');
                        $("#modal-payment-expires").text(info.expiresText || '请尽快完成支付');
    
                        // Show the modal
                        $("#paymentModal").show();

                        // --- Start polling for payment status ---
                        if (paymentCheckInterval) clearInterval(paymentCheckInterval); // 清除之前的定时器
                        if (orderNo !== 'N/A') {
                            paymentCheckInterval = setInterval(function() {
                                checkPaymentStatus(orderNo);
                            }, 3000); // 每3秒查询一次
                        }
    
                        $btn.prop('disabled', false).text('重新生成订单');
                    } else {
                        var errorMsg = result.Msg || '未知错误';
                        $("#debug-info").html('<div style="color:red">创建支付订单失败: ' + errorMsg + '</div>');
                        $btn.prop('disabled', false).text('确认充值');
                    }
                },
                error: function(xhr) {
                    $("#debug-info").html('<div style="color:red">创建支付订单失败: 请稍后重试</div>');
                    $btn.prop('disabled', false).text('确认充值');
                }
            });
        });
            
        // 默认选择第一个金额
        $(".amount-option").first().click();
        $(".payment-method").first().click();
    
        // Modal close logic
        var modal = $("#paymentModal");
        var span = $(".modal-close-button-final");
    
        function closeModal() {
            if (paymentCheckInterval) {
                clearInterval(paymentCheckInterval); // 关闭模态框时停止轮询
                paymentCheckInterval = null;
            }
            modal.hide();
        }

        span.click(function() {
            closeModal();
        });
    
        $(window).click(function(event) {
            if (event.target == modal[0]) {
                closeModal();
            }
        });

        function checkPaymentStatus(orderNo) {
            $.ajax({
                url: '/shop/CheckPaymentStatus',
                method: 'POST',
                data: JSON.stringify({ orderNo: orderNo }),
                contentType: 'application/json',
                success: function(res) {
                    if (res.Return && res.Status === 1) { // Status 1 means success
                        if (paymentCheckInterval) clearInterval(paymentCheckInterval);

                        // Update UI to show success
                        $('#modal-qrcode-image').html('');
                        $('#modal-payment-hint').html('<div style="color: #4CAF50; font-weight:bold; font-size: 1.2em;">支付成功！</div>');
                        $('#modal-payment-expires').text('点数已自动充值到您的账户。');

                        // Automatically close modal after a delay and refresh
                        setTimeout(function() {
                            closeModal();
                            loadContent('Welcome'); // 刷新欢迎页以更新点数
                        }, 2000);
                    }
                    // If status is 2 (failed), you could handle it here
                    // If status is 0 (pending), do nothing and let the interval continue
                },
                error: function() {
                    // Stop polling on error to avoid spamming the server
                    if (paymentCheckInterval) clearInterval(paymentCheckInterval); 
                }
            });
        }
    });
    </script>
    
<div class="main">
	<div class="text-light well" style="margin-bottom: 10px;">
		<div class="row well-small product-header">
			<div class="item-icon-block">
				<img src="/public/shop/images/tera-icons/<%= product.icon %>.png" class="icon_grade_<%= product.rareGrade %> item-icon" alt=""">
				<img src="/public/shop/images/icons/icon_grade_<%= product.rareGrade %>.png" class="item-icon-grade" alt="">
				<%_ if (product.tag !== null) { _%>
				<img src="/public/shop/images/icons/icon_tag_<%= product.tag %>.png" class="item-icon-tag" alt="">
				<%_ } _%>
			</div>
			<div class="text-block text-light" style="width: 520px;">
				<h5 class="item_grade_<%= product.rareGrade %>">
					<%- product.title || __("[unknown]") %>
					<%_ if (product.productDiscount > 0) { _%>
					<span class="badge discount-badge"><%= __("%s% off", product.productDiscount) %></span>
					<%_ } _%>
					<%_ if (product.accountDiscount > 0) { _%>
					<span class="badge user-discount-badge"><%= __("%s% off", product.accountDiscount) %></span>
					<%_ } _%>
				</h5>
				<small><%- product.description %></font></small>
			</div>
			<div class="text-right text-light">
				<button class="btn btn-warning" id="gift"><%= __("Buy as a gift") %></button>
			<%_ if (back !== false) { _%>
				&nbsp;<a href="#" class="btn btn2" id="back"><i class="fas fa-angle-double-left"></i> <%= __("Back") %>&nbsp;</a>
			<%_ } _%>
			</div>
		</div>
		<hr>
		<%_ if (product.tradable) { _%>
		<%= __("Can be trade with other players.") %><br>
		<%_ } else { _%>
		<%= __("Cannot be transferred to other players.") %><br>
		<%_ } _%>
		<%_ if (!product.warehouseStorable) { _%>
		<%= __("Can't put it in the warehouse.") %><br>
		<%_ } _%>
		<%_ if (product.requiredGender === "female") { _%>
		<%= __("For female characters only.") %><br>
		<%_ } _%>
		<%_ if (product.requiredGender === "male") { _%>
		<%= __("For male characters only.") %><br>
		<%_ } _%>
		<%_ if (product.requiredRace.length > 0) { _%>
		<%= __("For races") %>: <%= product.requiredRace.map(race => __(race)).join(", ") %><br>
		<%_ } _%>
		<%_ if (product.requiredClass.length > 0) { _%>
		<%= __("For classes") %>: <%= product.requiredClass.map(rclass => __(rclass)).join(", ") %><br>
		<%_ } _%>
		<%_ if (product.requiredLevel > 1) { _%>
		<%= __("Minimum character level") %>: <%= product.requiredLevel %>.<br>
		<%_ } _%>
		<%_ if (product.thumbnail) { _%>
		<br><a href="#" class="thumbnail" style="max-width: 280px;"><img src="/public/shop/images/screenshots/<%= product.thumbnail %>.jpg" alt="screenshot" style="max-height: 175px;"></a>
		<%_ } _%>
		<%_ if (product.items.length > 0) { _%>
		<br><table class="table" style="margin-bottom: 0;">
			<%_ product.items.forEach(item => { _%>
			<tr>
				<td style="width: 30px;">
					<div class="item-icon-block2">
						<img src="/public/shop/images/tera-icons/<%= item.icon %>.png" class="icon_grade_<%= item.rareGrade %> item-icon2" alt="">
						<img src="/public/shop/images/icons/icon_grade_<%= item.rareGrade %>.png" class="item-icon-grade2" alt="">
					</div>
				</td>
				<td class="name">
					<span class="item_grade_<%= item.rareGrade %>"><%- item.string || __("[unknown]") %></span> &ndash; <%- item.boxItemCount %> <%= __("pc.") %>
				</td>
			</tr>
			<%_ }) _%>
		</table>
		<%_ } _%>
		<%_ if (product.conversions.length > 0) { _%>
		<hr><%= __("Possible items") %>:<br>
		<br><table class="table" style="margin-bottom: 0;">
			<%_ product.conversions.forEach(conversion => { _%>
			<tr>
				<td style="width: 30px;">
					<div class="item-icon-block2">
						<img src="/public/shop/images/tera-icons/<%= conversion.icon %>.png" class="icon_grade_<%= conversion.rareGrade %> item-icon2" alt="">
						<img src="/public/shop/images/icons/icon_grade_<%= conversion.rareGrade %>.png" class="item-icon-grade2" alt="">
					</div>
				</td>
				<td class="name">
					<span class="item_grade_<%= conversion.rareGrade %>"><%- conversion.string || __("[unknown]") %></span>
				</td>
			</tr>
			<%_ }) _%>
		</table>
		<%_ } _%>
		<hr>
		<div class="recipient-info btn-warning" id="recipient_info" style="display: none;">
			<div class="recipient-info-left">
				<%= __("This product will be sent as a gift to the character:") %> 
				<span class="recipient-name" id="recipient_name"></span>
			</div>
			<div class="recipient-info-right">
				<button class="btn btn-small btn-secondary" id="gift_cancel_info"><%= __("Cancel") %></button>
			</div>
		</div>
		<div class="row buy-info white-text">
			<div class="span9 buy-block-left" id="buy_info">
				<div id="buy_descr" class="buy-descr">
					<div class="buy-descr-header">
						<%- __("Click the &quot;Buy&quot; button to get the item:") %><br>&nbsp;
						&ndash; <b class="item_grade_<%= product.rareGrade %>"><%- product.title %></b> <small>&#10005;</small> <b class="quantity-value">1</b>
					</div>
					<div class="buy-descr-content">
						<%- __("Your account will be debited") %>: <b class="price-value gold2"><%= product.price %></b> <s><b class="price-orig-value gold2"></b></s> <i class="fas fa-coins gold2"></i><br>
						<%- __("The item will appear in the &quot;Item Claim&quot; window.") %><br>
						<%- __("You can open it from the main menu in the &quot;Shop&quot; section the &quot;Item Claim&quot; option.") %>
					</div>
				</div>
				<form id="gift_form" class="buy-gift-form" method="POST" action="" style="display: none;">
					<div>
						<legend class="white-text">
							<%= __("Buy a Product as a Gift") %>
						</legend>
					</div>
					<div>
						<input type="text" placeholder="<%= __("Enter character nickname") %>" id="gift_character" style="margin-top: 10px;">
						<button type="submit" class="btn btn-warning" id="gift_search"><%= __("Search") %></button>&nbsp;
						<button class="btn btn-secondary" id="gift_cancel"><%= __("Cancel") %></button>
						<div class="alert alert-error gift-error fade in" style="display: none;" id="gift_error">
							<strong></strong>
						</div>
						<div class="gift-descr" id="gift_descr">
							<%= __("Enter the nickname of the character you want to buy the product for as a gift.") %>
						</div>
					</div>
				</form>
				<form id="coupon_form" class="coupon-form" method="POST" action="" style="display: none;">
					<div>
						<legend class="white-text">
							<%= __("Use Coupon") %>
						<%_ if (coupons.length > 0) { _%>
							<a href="#" class="btn btn-small btn-secondary" id="coupon_toggle_select" style="display: none;"><%= __("Choose from my coupons") %></a>
							<a href="#" class="btn btn-small btn-secondary" id="coupon_toggle_input"><%= __("Enter coupon manually") %></a>
						<%_ } _%>
						</legend>
					</div>
					<div>
						<select name="coupon_select" id="coupon_select" style="margin-top: 10px;<%= coupons.length === 0 ? ' display: none;' : '' %>">
						<%_ coupons.forEach(coupon => { _%>
							<option value="<%= coupon.get("coupon") %>"><%= coupon.get("coupon") %> (<%= __("%s% off", coupon.get("discount")) %>)</option>
						<%_ }) _%>
						</select>
						<input type="text" placeholder="<%= __("Enter your coupon") %>" name="coupon" id="coupon" style="margin-top: 10px;<%= coupons.length > 0 ? ' display: none;' : '' %>">
						<button type="submit" class="btn btn-primary" id="coupon_accept"><%= __("Accept") %></button>&nbsp;
						<button class="btn btn-secondary" id="coupon_cancel"><%= __("Cancel") %></button>
						<div class="alert alert-error coupon-error fade in" style="display: none;" id="coupon_error">
							<strong></strong>
						</div>
						<div class="coupon-descr" id="coupon_descr">
							<%= __("Here you can enter your coupon, which will give you a discount on this purchase.") %>
						</div>
					</div>
				</form>
			</div>
			<div class="span3 buy-block-right" id="buy_block">
				<div id="buy_available" style="margin-top: 0;">
					<%= __("Your price") %>: <span class="gold"><strong class="price-value"></strong> <i class="fas fa-coins"></i></span><br>
					<button class="btn btn-small btn-primary coupon-use"><%= __("Use coupon") %></button><br>
					<input id="quantity" type="number" value="1" min="1" max="<%= product.maxQuantity %>" step="1" class="quantity"> <button class="btn btn-large btn-danger" id="buy"><%= __("Buy") %></button>
				</div>
				<div id="buy_inprogress" style="display: none; margin-top: 45px;">
					<%= __("Purchase in progress") %>...
				</div>
				<div id="buy_nocoins" style="display: none; margin-top: 12px;">
					<%= __("You don't have enough funds to buy.") %><br>
					<span class="gold"><strong class="price-value"></strong> <i class="fas fa-coins"></i></span><br>
					<button class="btn btn-small btn-primary coupon-use"><%= __("Use coupon") %></button>
				</div>
				<div id="buy_success" style="display: none; margin-top: 12px;">
					<%= __("You have success purchased an item.") %><br><br>
					<%= __("Spent") %>: <span class="gold"><strong class="price-value"></strong> <i class="fas fa-coins"></i></span>
				</div>
				<div id="buy_error" style="display: none; margin-top: 20px;">
					<%= __("An error occurred while purchasing the product") %>: 
				</div>
			</div>
		</div>
	</div>
</div>
<%_ if (search) { _%>
<script type="text/javascript">
	var backParams = { search: "<%= search %>" };
</script>
<%_ } else { _%>
<script type="text/javascript">
	var backParams = { category: "<%= product.categoryId %>" };
</script>
<%_ } _%>
<script type="text/javascript">
	function getAccountBalance() {
		return parseInt($("#shop_balance").html());
	}

	// Product
	function initializeAndSetValues(product, hideBuyForm) {
		var accountBalance = getAccountBalance();
		if (hideBuyForm) {
			if (accountBalance < product.finalPrice * product.quantity) {
				$('#buy_available').hide();
				$('#buy_nocoins').show();
			} else {
				$('#buy_available').show();
				$('#buy_nocoins').hide();
			}
		}
		$('.quantity-value').html(product.quantity);
		$('.price-value').html(product.finalPrice * product.quantity);
		if (product.finalPrice != product.originalPrice) {
			$('.price-orig-value').html(product.originalPrice * product.quantity);
		}
		$('#quantity').prop('disabled', product.couponDiscount > 0).trigger('disabledChange');
		$('#buy').prop('disabled', accountBalance < product.finalPrice * product.quantity);
		$('#gift').attr('disabled', product.recipientUserId);
		$('.coupon-use').prop('disabled', product.quantity > 1);
	}
	function quantityChange(product) {
		product.quantity = $('#quantity').val();
		if (product.quantity < 1) product.quantity = 1;
		if (product.quantity > product.maxQuantity) product.quantity = product.maxQuantity;
		$('#quantity').val(product.quantity);
		$('.coupon-use').prop('disabled', product.quantity > 1);
		initializeAndSetValues(product, false);
	}
	function schollDown() {
		$("#content_product").animate({ scrollTop: $(document).height() }, 300);
	}

	// Gift
	function giftUse() {
		$('.coupon-use').prop('disabled', true);
		$('#gift').attr('disabled', true);
		$('#quantity').prop('disabled', true).trigger('disabledChange');
		$('#buy').prop('disabled', true);
		$('#buy_descr').hide();
		$('#gift_form').show();
		$('#gift_character').focus();
		schollDown();
	}
	function giftSearch(product) {
		if ($('#gift_character').val() == '') {
			giftErrorShow("<%= __('Please enter character nickname.') %>");
			return;
		}
		$('#gift_search').prop('disabled', true);
		$('#gift_cancel').prop('disabled', true);
		giftErrorHide();
		shopReqCharacterAction($('#gift_character').val(), function(result) {
			$('#gift_search').prop('disabled', false);
			$('#gift_cancel').prop('disabled', false);
			if (result.ReturnCode == 0) {
				product.recipientUserId = result.AccountId;
				giftRecipientInfoShow(result.Name, result.Class, result.Level);
				initializeAndSetValues(product, false);
				$('#gift_form').hide();
				$('#buy_descr').show();
				schollDown();
				return;
			}
			switch (result.ReturnCode) {
				case 5000:
					giftErrorShow("<%= __('The specified character was not found.') %>");
					break;
				case 5010:
					giftErrorShow("<%= __('The specified character belongs to you.') %>");
					break;
				default:
					giftErrorShow("<%= __('Search error') %>: #" + result.ReturnCode + " " + result.Msg);
			}
		});
	}
	function giftCancel(product) {
		product.recipientUserId = null;
		giftErrorHide();
		giftRecipientInfoHide();
		initializeAndSetValues(product, false);
		$('#gift').attr('disabled', false);
		$('#gift_character').val('');
		$('#gift_form').hide();
		$('#buy_descr').show();
	}
	function giftErrorShow(error) {
		$('#gift_descr').hide();
		$('#gift_error').show();
		$('#gift_error strong').html(error);
	}
	function giftErrorHide() {
		$('#gift_error strong').html('');
		$('#gift_error').hide();
		$('#gift_descr').show();
	}
	function giftRecipientInfoShow(name, class_, level) {
		$('#recipient_info #recipient_name').html("<b>" + name + "</b> (" + class_ + ", " + level + " <%= __('lvl.') %>)");
		$('#recipient_info').show();
	}
	function giftRecipientInfoHide() {
		$('#recipient_info').hide();
		$('#recipient_name').html('');
	}

	// Coupon
	function couponUse() {
		$('.coupon-use').prop('disabled', true);
		$('#gift_cancel_info').prop('disabled', true);
		$('#gift').attr('disabled', true);
		$('#buy').prop('disabled', true);
		$('#quantity').prop('disabled', true).trigger('disabledChange');
		$('#buy_descr').hide();
		$('#coupon_form').show();
	}
	function couponToggleSelect(product) {
		couponErrorHide();
		$('#coupon_toggle_select').hide();
		$('#coupon').hide();
		$('#coupon_select').trigger('showCustomSelect');
		$('#coupon_toggle_input').show();
		product.coupon = $('#coupon_select');
	}
	function couponToggleInput(product) {
		couponErrorHide();
		$('#coupon_toggle_input').hide();
		$('#coupon_select').trigger('hideCustomSelect');
		$('#coupon').show();
		$('#coupon_toggle_select').show();
		product.coupon = $('#coupon');
	}
	function couponAccept(product) {
		if (product.coupon.val() == '') {
			couponErrorShow("<%= __('Please enter your coupon.') %>");
			return;
		}
		$('#coupon_accept').prop('disabled', true);
		$('#coupon_cancel').prop('disabled', true);
		couponErrorHide();
		shopCouponAcceptAction(product.coupon.val(), product.productId, function(result) {
			$('#coupon_accept').prop('disabled', false);
			$('#coupon_cancel').prop('disabled', false);
			if (result.ReturnCode == 0) {
				product.quantity = 1;
				product.couponDiscount = result.Discount;
				product.finalPrice = calculatePriceWithDiscounts(
					product.originalPrice,
					[product.productDiscount, product.couponDiscount, product.accountDiscount]
				);
				initializeAndSetValues(product, true);
				$('.coupon-use').html(product.couponDiscount + "<%= '% ' + __('discount applied') %>");
				$('#gift_cancel_info').prop('disabled', false);
				$('#quantity').val(product.quantity);
				$('#buy').prop('disabled', false);
				$('#coupon_form').hide();
				$('#buy_descr').show();
				return;
			}
			switch(result.ReturnCode) {
				case 9:
					couponErrorShow("<%= __('Too many requests.') %>");
					break;
				case 1000:
					couponErrorShow("<%= __('The specified coupon does not exist.') %>");
					break;
				case 1001:
					couponErrorShow("<%= __('The specified coupon has expired.') %>");
					break;
				case 1002:
					couponErrorShow("<%= __('The specified coupon has reached its activation limit.') %>");
					break;
				case 1010:
					couponErrorShow("<%= __('The specified coupon has already been activated.') %>");
					break;
				default:
					couponErrorShow("<%= __('Activation error') %>: #" + result.ReturnCode + " " + result.Msg);
			}
		});
	}
	function couponCancel(product) {
		$('#coupon_accept').prop('disabled', true);
		$('#coupon_cancel').prop('disabled', true);
		shopCouponCancelAction(function(result) {
			product.couponDiscount = 0;
			product.finalPrice = calculatePriceWithDiscounts(
				product.originalPrice,
				[product.productDiscount, product.accountDiscount]
			);
			initializeAndSetValues(product, true);
			couponErrorHide();
			$('#coupon_accept').prop('disabled', false);
			$('#coupon_cancel').prop('disabled', false);
			$('.coupon-use').html("<%= __('Use coupon') %>");
			$('#buy').prop('disabled', false);
			$('#coupon_form').hide();
			$('#buy_descr').show();
			$('#coupon').val('');
			$('#coupon_select').prop("selectedIndex", 0);
			$('#gift_cancel_info').prop('disabled', false);
		});
	}
	function couponErrorShow(error) {
		$('#coupon_descr').hide();
		$('#coupon_error').show();
		$('#coupon_error strong').html(error);
	}
	function couponErrorHide() {
		$('#coupon_error strong').html('');
		$('#coupon_error').hide();
		$('#coupon_descr').show();
	}

	// Purchase
	function requestPurchaseStatus(logId) {
		var requestStatusInterval = setInterval(shopPurchaseStatusAction, 1000, logId, function(result) {
			$('#buy_available').hide();
			$('#buy_inprogress').hide();
			if (result.ReturnCode == 0) {
				if (result.Status == "deposit") {
					$('#buy_inprogress').show();
					return;
				}
				else if (result.Status == "completed") {
					$('#buy_success').show();
				}
				else if (result.Status == "rejected") {
					$('#buy_error').show().append('<br>#0 Transaction rejected');
				}
				loadAccountInfo();
			} else {
				$('#buy_error').show().append('<br>#' + result.ReturnCode + " " + result.Msg);
			}
			clearInterval(requestStatusInterval);
		});
	}
	function purchaseProduct(product) {
		$('.coupon-use').prop('disabled', true);
		$('#gift_cancel_info').prop('disabled', true);
		$('#gift').attr('disabled', true);
		$('#buy').prop('disabled', true);
		$('#quantity').prop('disabled', true).trigger('disabledChange');
		shopPurchaseAction(product.productId, product.quantity, product.recipientUserId, function(result) {
			$('#buy_available').hide();
			if (result.ReturnCode == 0) {
				$('#buy_inprogress').show();
				requestPurchaseStatus(result.LogId);
			} else if (result.ReturnCode == 1000) {
				$('#buy_nocoins').show();
			} else {
				$('#buy_inprogress').hide();
				$('#buy_error').show().append('<br>#' + result.ReturnCode + " " + result.Msg);
			}
		});
	}

	$(function() {
		var product = {
			recipientUserId: null,
			productId: <%= product.id %>,
			balance: getAccountBalance(),
			quantity: 1,
			maxQuantity: <%= product.maxQuantity %>,
			productDiscount: <%= product.productDiscount %>,
			accountDiscount: <%= product.accountDiscount %>,
			couponDiscount: 0,
			originalPrice: <%= product.price %>,
			finalPrice: <%= product.price %>,
			coupon: $('#coupon')
		};

		<%_ if (coupons.length > 0) { _%>
		product.coupon = $('#coupon_select');
		<%_ } _%>

		product.finalPrice = calculatePriceWithDiscounts(
			product.originalPrice, 
			[product.productDiscount, product.accountDiscount]
		);

		initializeAndSetValues(product, true);

		// Gift
		$('#gift').click(function() {
			giftUse();
			return false;
		});
		$('#gift_character').change(function() {
			giftErrorHide();
		});
		$('#gift_form').submit(function() {
			giftSearch(product);
			return false;
		});
		$('#gift_cancel, #gift_cancel_info').click(function() {
			giftCancel(product);
			return false;
		});

		// Coupon
		$('#coupon_toggle_select').click(function() {
			couponToggleSelect(product);
			return false;
		});
		$('#coupon_toggle_input').click(function() {
			couponToggleInput(product);
			return false;
		});
		$('#coupon, #coupon_select').change(function() {
			couponErrorHide();
		});
		$('.coupon-use').click(function() {
			couponUse();
			return false;
		});
		$('#coupon_form').submit(function() {
			couponAccept(product);
			return false;
		});
		$('#coupon_cancel').click(function() {
			couponCancel(product);
			return false;
		});

		// Buy
		$('#quantity').change(function() {
			quantityChange(product);
		});
		$('#buy').click(function() {
			purchaseProduct(product);
			return false;
		});

		$('#back').click(backToCatalog);
		$('a.thumbnail').click(function() {
			$('#image-modal .modal-body img').attr('src', $(this).find('img').attr('src'));
			$('#image-modal').modal('show');
			return false;
		});
		$('#image-modal .modal-body img').click(function() {
			$('#image-modal').modal('hide');
		});
		$('input[type="number"]').numberInput();
		$('select').customSelect();
	});
</script>
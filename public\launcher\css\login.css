﻿@charset "utf-8";
/* CSS Document */
/* http: //meyerweb.com/eric/tools/css/reset/ v2.0 | 20110126 License: none (public domain) */
html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {
	margin: 0;
	padding: 0;
	border: 0;
	font-size: 100%;
	font: inherit;
	vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, footer, header, hgroup, menu, nav, section {
	display: block;
}
body {
	line-height: 1;
}
ol, ul {
	list-style: none;
}
blockquote, q {
	quotes: none;
}
blockquote:before, blockquote:after, q:before, q:after {
	content: '';
	content: none;
}
table {
	border-collapse: collapse;
	border-spacing: 0;
}
input, select, button {
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-app-region: no-drag;
}
body {
	background-color: transparent;
	background-repeat: no-repeat;
	background-position: 0px 0px;
	font-size: 12px;
	-webkit-app-region: drag;
}
body, button {
	font-family: Helvetica, Arial, sans-serif;
}
button {
	outline: none;
}
a {
	text-decoration: none;
	color: #fff;
	-webkit-app-region: no-drag;
}
.loader {
	background-image: url(../images/loader.gif);
	background-size: 32px;
	background-repeat: no-repeat;
	width: 32px;
	height: 32px;
	margin: 200px auto;
}
.wrap {
	top: 0;
	left: 0;
	background-image: url(../images/bg.jpg);
	background-repeat: no-repeat;
	background-position: 0px 0px;
	width: 325px;
	height: 550px;
	position: relative;
	padding-top: 1px;
	background-color: transparent;
	-webkit-app-region: drag;
}
.form-horizontal {
	width: 220px;
	margin: 105px auto 0;
	display: block;
	-webkit-app-region: no-drag;
}
.form-control {
	background-image: url(../images/form.png);
	background-repeat: no-repeat;
	width: 220px;
	height: 42px;
	line-height: 42px;
	font-size: 16px;
	color: #303030;
	margin-bottom: 8px;
	border-width: 0;
	background-color: transparent;
	padding-left: 50px;
	-webkit-app-region: no-drag;
}
.form-control.form-code {
	background-position: 0 -150px;
}
.form-control.form-email {
	background-position: 0 -100px;
}
.form-control.form-password {
	background-position: 0 -50px;
}
.checkbox {
	float: left;
	line-height: 30px;
}
.forgot-password {
	float: right;
	line-height: 30px;
	padding-right: 5px;
}
.forgot-password a {
	text-decoration: underline;
	color: #8ea8df;
}
.forgot-password a:hover {
	text-decoration: none;
	color: #8ea8df;
}
.form-error {
	margin-top: 200px;
	text-align: center;
	font-size: 18px;
}
.form-error h1 {
	margin-bottom: 20px;
	font-weight: bold;
}
.quick_link {
	float: right;
	line-height: 30px;
}
.btn-close {
	cursor: pointer;
	width: 22px;
	height: 22px;
	border-width: 0;
	background-image: url(../images/btn-close1.png);
	background-position: 0px 0px;
	position: absolute;
	top: 15px;
	right: 25px;
	text-indent: -9999px;
	background-color: transparent;
	-webkit-app-region: no-drag;
}
.btn-close:hover {
	background-position: 0px -22px;
}
button.btn-orange {
	background-image: url(../images/btn.png);
	width: 224px;
	height: 67px;
	color: #fff2a7;
	font-size: 24px;
	background-color: transparent;
	border-width: 0;
	padding: 0;
	line-height: 0;
	display: block;
	cursor: pointer;
	margin-bottom: 5px;
	text-shadow: 1px 1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, -1px -1px 0 #000, 1px 0px 0 #000, 
		0px 1px 0 #000, -1px 0px 0 #000, 0px -1px 0 #000, 1px 1px 0px rgba(0,0,0,0);
}
button.btn-orange:hover {
	background-position: 224px 0;

}
button.btn-blue {
	background-image: url(../images/btn.png);
	width: 224px;
	height: 67px;
	color: #fff;
	font-size: 24px;
	background-color: transparent;
	border-width: 0;
	padding: 0;
	line-height: 0;
	display: block;
	cursor: pointer;
	margin-bottom: 5px;
	background-position: 0 -70px;
	text-shadow: 1px 1px 0 #000, 1px -1px 0 #000, -1px 1px 0 #000, -1px -1px 0 #000, 1px 0px 0 #000,
		0px 1px 0 #000, -1px 0px 0 #000, 0px -1px 0 #000, 1px 1px 0px rgba(0,0,0,0);
}
button.btn-blue:hover {
	background-position: 224px -70px;
}
.msg-modal {
	display: none;
	position: absolute;
	margin-left: 30px;
	margin-top: 45px;
	padding: 8px 10px;
	width: 240px;
	font-size: 14px;
	line-height: 16px;
	color: #fff;
	text-align: center;
	border-radius: 5px;
	z-index: 9999;
	box-shadow: 0 1px 5px rgba(0,0,0,0.9);
	text-shadow: 0 0 1px rgba(0,0,0,0.5);
	cursor: default;
	/* opacity: 0.97; */
}
.msg-modal.success {
	background: rgba(0, 153, 0, 0.15);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(0, 187, 0, 0.3);
}
.msg-modal.error {
	background: rgba(204, 51, 51, 0.15);
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	border: 1px solid rgba(204, 102, 102, 0.3);
}
/* hCaptcha 缩放样式 - 适应220px宽度 */
.h-captcha {
	transform: scale(0.724);
	transform-origin: center;
	margin: 0 auto;
	width: 304px;
	height: 78px;
}

.form-group .h-captcha {
	margin-left: -42px; /* 调整位置使其居中 */
}
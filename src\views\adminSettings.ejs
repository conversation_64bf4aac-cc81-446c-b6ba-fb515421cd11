<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("API Settings") %></h1>
	</div>
	<section class="section">
		<div class="row">
			<div class="col-md-6">
				<div class="card">
					<div class="card-block">
						<div class="card-title-block">
							<h3 class="title"><%= __("Versions Information") %></h3>
						</div>
						<section class="mb-2">
							<%= __("Node.js Version") %>: <strong><%= process.version %></strong>
						</section>
						<section class="mb-2">
							<%= __("TERA API Version") %>: <strong><%= versions.app %></strong>
						</section>
						<section class="mb-2">
							<%= __("DB Version") %>: <strong><%= versions.db %></strong>
						</section>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<div class="card">
					<div class="card-block">
						<div class="card-title-block">
							<h3 class="title"><%= __("Configuration Variables") %></h3>
						</div>
						<section class="table-responsive">
							<table class="table table-hover">
								<thead>
									<tr>
										<th><%= __("Parameter") %></th>
										<th><%= __("Value") %></th>
									</tr>
								</thead>
								<tbody>
									<%_ settings.forEach(setting => {
										let value = setting.value;

										if (/(_SECRET|_IV|_KEY|_KEYS|_PASSWORD)$/i.test(setting.parameter)) {
											value = `<span class="text-muted">[${__("hidden")}]</span>`;
										}
										if (/^(true|false)$/i.test(value)) {
											value = `<strong>${/^true$/i.test(value) ? "true" : "false"}</strong>`;
										}
										if (value == "") {
											value = `<span class="text-muted">[${__("unconfigured")}]<span>`;
										}
									_%>
									<tr>
										<td><%= setting.parameter %></td>
										<td><%- value %></td>
									</tr>
									<%_ }) _%>
								</tbody>
							</table>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
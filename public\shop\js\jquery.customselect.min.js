/*! jQuery Custom Select v1.0.1 | MIT License | (c) 2025 Non-commercial Project "HSDN" */
!function(t){var e=t.fn.prop;t.fn.prop=function(n,o){return"selectedIndex"===n&&void 0!==o?(this.each(function(){var c=t(this),i=c.prop(n);e.call(c,n,o),i!==o&&c.trigger("change")}),this):e.apply(this,arguments)}}(window.jQuery),function(t){t.fn.customSelect=function(){return this.each(function(){var e=t(this),n=t('<div class="custom-select"></div>'),o=t('<div class="custom-select-selected"></div>'),c=t('<div class="custom-select-arrow">&#9660;</div>'),i=t('<div class="custom-select-options"></div>');function s(){o.off("click"),e.prop("disabled")?n.addClass("custom-select-disabled"):(n.removeClass("custom-select-disabled"),o.on("click",function(t){t.stopPropagation(),i.toggle()}))}o.text(e.find("option:selected").text()),n.append(o),o.append(c),e.find("option").each(function(n){var s=t(this),d=t('<div class="custom-select-option"></div>');d.text(s.text()),d.attr("data-value",s.val()),d.on("click",function(){e.prop("disabled")||(e.prop("selectedIndex",n).trigger("change"),o.text(s.text()),o.append(c),i.hide())}),i.append(d)}),n.append(i),e.css({position:"absolute",left:"-9999px",top:"-9999px"}),e.after(n),"none"===e.css("display")&&n.hide(),s(),e.on("showCustomSelect",function(){n.show()}),e.on("hideCustomSelect",function(){n.hide()}),e.on("updateDisabledState",function(){s()}),t(document).on("click",function(e){t(e.target).closest(n).length||i.hide()}),e.on("change",function(){var t=e.prop("selectedIndex"),n=e.find("option").eq(t).text();o.text(n),o.append(c)})})}}(window.jQuery);
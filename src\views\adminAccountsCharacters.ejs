<%_
const genderId = [
	__("Male"),
	__("Female")
];
const raceId = [
	__("Human"),
	__("High Elf"),
	__("<PERSON><PERSON>"),
	__("<PERSON><PERSON><PERSON>"),
	__("<PERSON><PERSON>"),
	__("<PERSON><PERSON>"),
	__("<PERSON><PERSON>")
];
const classId = [
	__("Warrior"),
	__("<PERSON><PERSON>"),
	__("Slayer"),
	__("<PERSON><PERSON><PERSON><PERSON>"),
	__("Sorcerer"),
	__("Archer"),
	__("<PERSON>"),
	__("Mystic"),
	__("<PERSON>"),
	__("<PERSON>"),
	__("Brawler"),
	__("<PERSON>"),
	__("Valkyrie")
];
_%>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Account Characters List") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="accountDBID"><%= __("Account ID") %></label>
				<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>">
			</div>
			<div class="form-group">
				<label for="serverId"><%= __("Server ID") %></label>
				<select class="form-control boxed" name="serverId">
				<%_ servers.forEach(s => { _%>
					<option value="<%= s.get("serverId") %>" <%= s.get("serverId") == serverId ? "selected" : "" %>>(<%= s.get("serverId") %>) <%= s.get("nameString") %></option>
				<%_ }) _%>
				</select>
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
	</section>
	<%_ if (characters === null) { _%>
	<section class="section">
		<div class="alert alert-info">
			<%= __("Please enter account ID and select the server.") %>
		</div>
	</section>
	<%_ } else { _%>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th><%= __("Name") %></th>
											<th><%= __("Level") %></th>
											<th><%= __("Race") %></th>
											<th><%= __("Gender") %></th>
											<th><%= __("Class") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Server ID") %></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
	<%_ } _%>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

(characters || []).forEach(character => {
	tableData.push([
		character.get("characterId"),
		character.get("name") || "-",
		character.get("level") || "-",
		raceId[character.get("raceId") == 4 && character.get("genderId") == 1 ? 6 : character.get("raceId")] || "-",
		genderId[character.get("genderId")] || "-",
		classId[character.get("classId")] || "-",
		character.get("accountDBID"),
		character.get("info")?.get("userName") || "-",
		`(${character.get("serverId")}) ${character.get("server")?.get("nameString") || ""}`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable
		}));
		$(".section").show();
	});
</script>
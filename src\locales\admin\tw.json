{"err_1044481": "STEER连接错误。", "err_1044482": "无法注册STEER。", "err_1044483": "STEER未注册。", "err_1044484": "STEER读取数据错误。", "err_1044485": "STEER写入数据错误。", "err_16778215": "STEER会话已过期。请重新登录。", "err_16778216": "无法连接到STEER系统。请检查服务器状态。", "err_16778217": "通信过程中发生障碍。请检查服务器状态。", "err_16778218": "您没有可用的权限。请联系管理员。", "err_16778219": "未知错误。请联系管理员。", "err_16778220": "您没有权限。请联系管理员。", "err_16778221": "本地化数据无效。请联系管理员。", "err_16778222": "数据无效。请联系管理员。", "err_16778223": "发生异常情况。请联系管理员。", "err_33554436": "服务器未连接。", "err_33554437": "超出数据传输时间限制", "err_83885830": "发生数据库错误。", "err_83886080": "正常处理。", "err_83886180": "ID或密码不匹配。", "err_83886280": "您没有权限或功能未配置为执行类型", "err_83886281": "用户组处于非活动状态", "err_83886282": "功能组处于非活动状态", "err_83886283": "功能处于非活动状态", "err_83886284": "目标组没有执行功能的权限或功能存在问题。", "err_83886285": "未定义功能调用类型", "err_83886286": "您没有权限或功能未配置为投射类型", "err_83886287": "投射状态已更改或不属于投射目标组", "err_83886288": "投射状态已更改或投射不存在", "err_83886289": "不属于检查组", "err_83886290": "目标数据不存在", "err_83886291": "重复登录。先前的登录会话已终止。", "err_83886292": "会话已过期。", "err_83886293": "客户端IP已更改。请重新登录。", "err_83886294": "账户已存在。", "err_83886295": "用户无法登录。", "err_167771910": "发生数据库错误。", "Invalid QA login or password.": "无效的QA登录或密码。", "The session has expired.": "会话已过期。", "Function access denied": "功能访问被拒绝", "Operation failed": "操作失败", "The field must contain the value as a number.": "该字段必须包含数字值。", "The field contains an existing server ID.": "该字段包含现有的服务器ID。", "The field must contain a valid IP value.": "该字段必须包含有效的IP值。", "The field must contain a valid port value.": "该字段必须包含有效的端口值。", "The field must be a valid value.": "该字段必须是有效值。", "The field contains already existing language code.": "该字段包含已存在的语言代码。", "The field must be between 1 and 256 characters.": "该字段必须在1到256个字符之间。", "The field has invalid value.": "该字段的值无效。", "Sync server state": "同步服务器状态", "Sync server state with the PlanetDB database after saving": "保存后与PlanetDB数据库同步服务器状态", "Sync server state with the PlanetDB database is not configured.": "未配置与PlanetDB数据库同步服务器状态。", "The field must be between 1 and 1024 characters.": "该字段必须在1到1024个字符之间。", "The field must be between 1 and 50 characters.": "该字段必须在1到50个字符之间。", "The field must be between 1 and 2048 characters.": "该字段必须在1到2048个字符之间。", "The field must contain a valid date.": "该字段必须包含有效日期。", "The field contains a date that is too late.": "该字段包含的日期太晚。", "The field must be between 3 and 24 characters.": "该字段必须在3到24个字符之间。", "The field must be between 8 and 128 characters.": "该字段必须在8到128个字符之间。", "The field must contain a valid email.": "该字段必须包含有效的电子邮件。", "The field must contain a valid number.": "该字段必须包含有效数字。", "Added benefit already exists.": "已添加的福利已存在。", "The field contains existing benefit ID on account.": "该字段包含账户上的现有福利ID。", "The field contains invalid function.": "该字段包含无效功能。", "The field contains not existing promo code ID.": "该字段包含不存在的促销代码ID。", "The field contains inactive promo code ID.": "该字段包含非活动的促销代码ID。", "The field contains expired promo code ID.": "该字段包含过期的促销代码ID。", "The field contains the promo code ID with the activation limit reached.": "该字段包含已达到激活限制的促销代码ID。", "The field must be between 6 and 20 characters.": "该字段必须在6到20个字符之间。", "The field must be between 3 and 8 characters.": "该字段必须在3到8个字符之间。", "The field contains not existing account ID.": "该字段包含不存在的账户ID。", "The field contains an existing promo code.": "该字段包含现有的促销代码。", "The field contains an existing coupon.": "该字段包含现有的优惠券。", "Shop account with specified account ID already exists.": "指定账户ID的商店账户已存在。", "This promo code has already been activated on the specified account ID.": "此促销代码已在指定账户ID上激活。", "The field must be between 1 and 255 characters.": "该字段必须在1到255个字符之间。", "No items have been added to the product.": "产品中未添加任何项目。", "A non-existent item has been added": "已添加不存在的项目", "Added item already exists.": "已添加的项目已存在。", "The field contains already banned account ID.": "该字段包含已被禁止的账户ID。", "There are no Service Items for the specified service item IDs.": "指定服务项目ID没有服务项目。", "No items have been added to the box.": "盒子中未添加任何项目。", "The field contains not existing character ID.": "该字段包含不存在的角色ID。", "The field contains not existing server ID.": "该字段包含不存在的服务器ID。", "Task with this box ID is already running. Check tasks queue.": "此盒子ID的任务已在运行。检查任务队列。", "The field contains already existing name.": "该字段包含已存在的名称。", "The field contains already existing email.": "该字段包含已存在的电子邮件。", "The field must contain an existing category ID.": "该字段必须包含现有的类别ID。", "Profile": "个人资料", "Logout": "注销", "Dashboard": "仪表板", "Maintenance": "维护", "Servers": "服务器", "Servers List (SLS)": "服务器列表（SLS）", "Strings": "字符串", "Accounts": "账户", "Benefits": "福利", "Characters": "角色", "Reports": "报告", "Game Reports": "游戏报告", "Activity": "活动", "Cheats": "作弊", "Chronoscrolls": "时间卷轴", "Shop": "商店", "Categories": "类别", "Products": "产品", "Shop Logs": "商店日志", "Fund Logs": "资金日志", "Pay Logs": "支付日志", "Promo Codes": "促销代码", "Coupons": "优惠券", "List": "列表", "Activated": "已激活", "API Settings": "API设置", "Yes": "是", "No": "否", "Confirm": "确认", "unconfigured": "未配置", "hidden": "隐藏", "optional": "可选", "Confirm Deletion": "确认删除", "Are you sure you want to delete this entries?": "您确定要删除这些条目吗？", "No data available in table": "表中无可用数据", "Showing _START_ to _END_ of _TOTAL_ entries": "显示_TOTAL_条目中的_START_到_END_", "Showing 0 to 0 of 0 entries": "显示0到0条目中的0条目", "(filtered from _MAX_ total entries)": "（从_MAX_总条目中过滤）", "Show _MENU_ entries": "显示_MENU_条目", "Loading...": "加载中...", "Filter": "过滤", "No matching records found": "未找到匹配的记录", "First": "第一", "Last": "最后", "Next": "下一个", "Previous": "上一个", "activate to sort column ascending": "激活以按升序排序列", "activate to sort column descending": "激活以按降序排序列", "Please enter login": "请输入登录名", "Please enter password": "请输入密码", "Your Profile": "您的个人资料", "Session Information": "会话信息", "Permitted Functions": "允许的功能", "User login ID": "用户登录ID", "User login SN": "用户登录SN", "Function name": "功能名称", "Authentication type": "认证类型", "Time zone": "时区", "Login ID": "登录ID", "Password": "密码", "Save account information": "保存账户信息", "Login": "登录", "Show full report": "显示完整报告", "Show full log": "显示完整日志", "Servers Stats": "服务器统计", "Under maintenance": "维护中", "Server Maintenance": "服务器维护", "Add new maintenance plan": "添加新维护计划", "ID": "ID", "Start time": "开始时间", "End time": "结束时间", "Description": "描述", "Add Server Maintenance Plan": "添加服务器维护计划", "Back to list": "返回列表", "Add": "添加", "Edit Server Maintenance Plan": "编辑服务器维护计划", "Save": "保存", "Server List (SLS)": "服务器列表（SLS）", "Add new server": "添加新服务器", "Login IP:port": "登录IP:端口", "Name": "名称", "Language": "语言", "Type": "类型", "Enabled": "启用", "Crowdness": "拥挤度", "Available": "可用", "Users online": "在线用户", "Users total": "总用户", "Add Server": "添加服务器", "Server ID": "服务器ID", "Language code": "语言代码", "Login IP": "登录IP", "Login port": "登录端口", "Name string": "名称字符串", "Description string": "描述字符串", "Threshold low": "低阈值", "Threshold medium": "中阈值", "Only PvE": "仅PvE", "Is crowdness": "是拥挤度", "Is available": "是可用", "Is enabled": "是启用", "Edit Server": "编辑服务器", "Server List Strings": "服务器列表字符串", "Add new strings": "添加新字符串", "Game language": "游戏语言", "Category PvE": "类别PvE", "Category PvP": "类别PvP", "Server offline": "服务器离线", "Server low": "服务器低", "Server medium": "服务器中", "Server high": "服务器高", "Crowdness yes": "是拥挤度", "Popup": "弹出窗口", "Add Server Strings": "添加服务器字符串", "Edit Server Strings": "编辑服务器字符串", "Create new account": "创建新账户", "User name": "用户名", "Email": "电子邮件", "Registered": "已注册", "Permission": "权限", "Privilege": "特权", "Last login": "最后登录", "Last server": "最后服务器", "Last IP": "最后IP", "QA": "QA", "GM": "GM", "MT": "MT", "PL": "PL", "Banned": "已禁止", "Add Account": "添加账户", "Account Information": "账户信息", "Account Benefits": "账户福利", "Benefit ID": "福利ID", "None": "无", "Available until": "有效期至", "Add benefit": "添加福利", "Edit Account": "编辑账户", "Ban account": "禁止账户", "Quick Actions": "快速操作", "Send Box": "发送盒子", "Add Benefit": "添加福利", "Add Product": "添加产品", "Add Category": "添加类别", "Start Maintenance": "开始维护", "Reset": "重置", "Set GM": "设置GM", "Set QA": "设置QA", "Set MT": "设置MT", "Set PL": "设置PL", "Account Benefits List": "账户福利列表", "Account ID": "账户ID", "Show": "显示", "Add new benefit": "添加新福利", "Please enter account ID.": "请输入账户ID。", "Benefit description": "福利描述", "Add Account Benefit": "添加账户福利", "Edit Account Benefit": "编辑账户福利", "Male": "男性", "Female": "女性", "Human": "人类", "High Elf": "高等精灵", "Aman": "阿曼", "Castanic": "卡斯塔尼克", "Popori": "波波利", "Baraka": "巴拉卡", "Elin": "艾琳", "Warrior": "战士", "Lancer": "枪骑士", "Slayer": "屠杀者", "Berserker": "狂战士", "Sorcerer": "魔法师", "Archer": "弓箭手", "Priest": "牧师", "Mystic": "秘术师", "Reaper": "收割者", "Gunner": "炮手", "Brawler": "斗士", "Ninja": "忍者", "Valkyrie": "女武神", "Common": "普通", "Uncommon": "不常见", "Rare": "稀有", "Superior": "优越", "Mythic": "神话", "Account Characters List": "账户角色列表", "Please enter account ID and select the server.": "请输入账户ID并选择服务器。", "Level": "等级", "Race": "种族", "Gender": "性别", "Class": "职业", "Activity Report": "活动报告", "Period from": "期间从", "to": "到", "All": "全部", "Report time": "报告时间", "Action (playtime)": "动作（游戏时间）", "Client IP": "客户端IP", "Create": "创建", "Edit": "编辑", "Delete": "删除", "Characters Report": "角色报告", "Action": "动作", "Cheats Report": "作弊报告", "Cheat info": "作弊信息", "Character": "角色", "Skill": "技能", "Hits": "命中", "Limit": "限制", "Target": "目标", "Location": "位置", "Other": "其他", "Chronoscrolls Report": "时间卷轴报告", "Chrono ID": "时间卷轴ID", "Info": "信息", "Shop Accounts": "商店账户", "Accounts are created automatically when funds are deposited to the user's Shop balance.": "当资金存入用户的商店余额时，账户会自动创建。", "If additional information is not set, the information for the product will be taken from the first item added.": "如果未设置附加信息，产品的信息将从添加的第一个项目中获取。", "In the Last login field, the date of the last entry into the game is specified. Box will not be sent to users who logged into the game before this date.": "在最后登录字段中，指定了最后一次进入游戏的日期。盒子不会发送给在此日期之前登录游戏的用户。", "Balance": "余额", "Discount": "折扣", "Active": "活跃", "Valid": "有效", "Activations": "激活次数", "No change": "无变化", "Inactive": "不活跃", "Enter game": "进入游戏", "Leave game": "离开游戏", "count": "计数", "example": "示例", "Shop Categories": "商店类别", "Add new category": "添加新类别", "Sort": "排序", "Sort.": "排序。", "Title": "标题", "Add Shop Cetegory": "添加商店类别", "Category Title": "类别标题", "en": "英语", "ru": "俄语", "cn": "中文", "fr": "法语", "de": "德语", "jp": "日语", "kr": "韩语", "se": "瑞典语", "th": "泰语", "tw": "中文（台湾）", "us": "英语（美国）", "Edit Shop Category": "编辑商店类别", "Slides": "幻灯片", "Shop Slides": "商店幻灯片", "Add new slide": "添加新幻灯片", "Prio.": "优先级", "Priority": "优先级", "Product title": "产品标题", "Display start date": "显示开始日期", "Display end date": "显示结束日期", "Background image": "背景图片", "Preview": "预览", "Upload new": "上传新", "Required resolution: %sx%sx": "所需分辨率：%sx%sx", "not used": "未使用", "File upload": "文件上传", "Remove image": "移除图片", "Background image file not selected.": "未选择背景图片文件。", "The resolution must be: %sx%s": "分辨率必须是：%sx%s", "Only JPG and PNG files are allowed": "仅允许JPG和PNG文件", "The file is too big! Max size: %s MB": "文件太大！最大尺寸：%s MB", "File too large": "文件太大", "This image is used in the slide.": "此图片用于幻灯片中。", "Error deleting image.": "删除图片时出错。", "Add Shop Slide": "添加商店幻灯片", "Edit Shop Slide": "编辑商店幻灯片", "Shop Pay Logs": "商店支付日志", "Shop Fund Logs": "商店资金日志", "Log time": "日志时间", "Quantity": "数量", "Amount": "金额", "Operation description": "操作描述", "Product ID": "产品ID", "Box ID": "盒子ID", "Price": "价格", "Tag": "标签", "Status": "状态", "Updated": "已更新", "Shop Promo Codes": "商店促销代码", "Create new promo code": "创建新促销代码", "Promo code": "促销代码", "Assigned function": "分配的功能", "Valid from": "有效期从", "Valid to": "有效期至", "Tag valid from": "标签有效期从", "Tag valid to": "标签有效期至", "Discount valid from": "折扣有效期从", "Discount valid to": "折扣有效期至", "Maximum of activations": "最大激活次数", "no limit on the number of activations.": "激活次数没有限制。", "Add Shop Promo Code": "添加商店促销代码", "Promo Code Information": "促销代码信息", "Promo Code Description": "促销代码描述", "Edit Shop Promo Code": "编辑商店促销代码", "Shop Activated Promo Codes": "商店已激活促销代码", "Promo code ID": "促销代码ID", "Activate promo code": "激活促销代码", "Please enter account ID or promo code ID.": "请输入账户ID或促销代码ID。", "Activation time": "激活时间", "Activate Promo Code": "激活促销代码", "Activate": "激活", "Shop Coupons": "商店优惠券", "Add Shop Coupon": "添加商店优惠券", "Edit Shop Coupon": "编辑商店优惠券", "Coupon Information": "优惠券信息", "Shop Activated Coupons": "商店已激活优惠券", "Create new coupon": "创建新优惠券", "Coupon": "优惠券", "Coupon ID": "优惠券ID", "Please enter account ID or coupon ID.": "请输入账户ID或优惠券ID。", "Configuration Variables": "配置变量", "Parameter": "参数", "Value": "值", "Add Shop Account": "添加商店账户", "Edit Shop Account": "编辑商店账户", "Shop Products": "商店产品", "Category": "类别", "Category ID": "类别ID", "Items count": "项目数量", "Icon": "图标", "Published": "已发布", "Add new product": "添加新产品", "Add Shop Product": "添加商店产品", "Edit Shop Product": "编辑商店产品", "Product Information": "产品信息", "Product Items": "产品项目", "Add item": "添加项目", "Tag Information": "标签信息", "Discount Information": "折扣信息", "Additional Information": "附加信息", "Rare grade": "稀有等级", "Item count": "项目数量", "Item ID": "项目ID", "Item": "项目", "Service item ID": "服务项目ID", "Count": "计数", "Validate form": "验证表单", "Item template ID": "项目模板ID", "Registered Users": "注册用户", "Online Users": "在线用户", "completed": "已完成", "deposit": "存款", "rejected": "已拒绝", "PromoCode": "促销代码", "ChronoScroll": "时间卷轴", "ShopApi": "商店API", "SignUp": "注册", "AccountDeletion": "账户删除", "BalanceChange": "余额变动", "Buy": "购买", "BuyCancel": "取消购买", "Bans": "禁止", "Account Bans": "账户禁止", "Edit Ban Account": "编辑禁止账户", "Ban Account": "禁止账户", "Kick all online players": "踢出所有在线玩家", "Operations": "操作", "Gateway API": "网关API", "Admin Operations Report": "管理员操作报告", "Gateway API Report": "网关API报告", "Function": "功能", "Endpoint": "端点", "IP address": "IP地址", "View Operations Report": "查看操作报告", "View Gateway API Report": "查看网关API报告", "Parameters": "参数", "Operation Parameters": "操作参数", "Request Parameters": "请求参数", "Item Claim": "物品领取", "Send": "发送", "Send Online": "在线发送", "Send All": "发送全部", "Tasks Queue": "任务队列", "Queue": "队列", "Items (Box)": "项目（盒子）", "Item Claim Boxes": "物品领取盒子", "Add new box": "添加新盒子", "Days": "天", "Add Item Claim Box": "添加物品领取盒子", "Box Information": "盒子信息", "Box Items": "盒子项目", "Edit Item Claim Box": "编辑物品领取盒子", "Items": "项目", "Admin Tasks Queue": "管理员任务队列", "Admin Tasks Queue Log": "管理员任务队列日志", "Restart tasks queue": "重启任务队列", "Cancel all tasks": "取消所有任务", "Cancel failed tasks": "取消失败任务", "Show tasks log": "显示任务日志", "Message": "消息", "Created": "已创建", "Pending": "待处理", "Failed": "失败", "Cancelled": "已取消", "Success": "成功", "Time": "时间", "Task ID": "任务ID", "Task name": "任务名称", "Task tag": "任务标签", "Send Item Claim Box": "发送物品领取盒子", "Recipient Information": "接收者信息", "Character ID": "角色ID", "Send Item Claim Box (All Users)": "发送物品领取盒子（所有用户）", "Results of Sending Item Claim Box": "发送物品领取盒子的结果", "Show tasks": "显示任务", "Operation has errors during execution": "操作执行过程中出现错误", "Operation completed successfully.": "操作成功完成。", "Operation is running in the background...": "操作正在后台运行...", "Rejected with message": "拒绝消息", "Boxes": "盒子", "Log": "日志", "Logs": "日志", "Item Claim Boxes Logs": "物品领取盒子日志", "Show all tasks": "显示所有任务", "Log ID": "日志ID", "Box": "盒子", "Chronoscroll": "时间卷轴", "[unknown]": "[未知]", "With selected": "选择的", "d.": "天", "hr.": "小时", "min.": "分钟", "sec.": "秒", "Change password": "更改密码", "The API settings contain a dynamic overriding of the server information.": "API设置包含服务器信息的动态覆盖。", "Launcher": "启动器", "Launcher Logs": "启动器日志", "Version": "版本", "Crash game": "游戏崩溃", "Exit game": "退出游戏", "Sign in": "登录", "Start download": "开始下载", "Finish download": "完成下载", "Downloading": "下载中", "Versions Information": "版本信息", "Node.js Version": "Node.js版<PERSON>", "TERA API Version": "TERA API版本", "DB Version": "数据库版本"}
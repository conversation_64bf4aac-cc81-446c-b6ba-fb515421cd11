<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge;">
<title>Login</title>
<link rel="stylesheet" href="/public/launcher/css/login.css">
<script type="text/javascript" src="/public/launcher/js/jquery-1.11.1.min.js"></script>
<script>
	var PATCH_URL = "<%= patchUrl %>";
</script>
<script type="text/javascript" src="/public/launcher/js/launcher-util.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher-errors.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher.js"></script>
<script>
	// hCaptcha 回调函数
	function onResetPasswordCaptchaSuccess(token) {
		console.log('hCaptcha 验证成功');
		window.hcaptchaToken = token;
	}

	function onResetPasswordCaptchaExpired() {
		console.log('hCaptcha 验证过期');
		window.hcaptchaToken = null;
	}

	function onResetPasswordCaptchaError(error) {
		console.error('hCaptcha 验证错误:', error);
		window.hcaptchaToken = null;
	}

	function resetPasswordAction(email) {
		var result = LauncherAPI.resetPasswordAction(email, window.hcaptchaToken);

		if (result) {
			if (result.Return) {
				Launcher.goTo("ResetPasswordVerifyForm?locale=<%= __req.query.locale %>");
			} else {
				switch (result.ReturnCode) {
					case 9:
						Launcher.showError("<%= __('Too many requests.') %>");
						break;
					case 10:
						Launcher.showError("<%= __('Please enter your email or login address.') %>");
						break;
					case 11:
						Launcher.showError("<%= __('This email or login is not exists.') %>");
						break;
					case 12:
						Launcher.showError("<%= __('Please confirm that you are human.') %>");
						break;
					default:
						Launcher.showError(result.ReturnCode + ": " + result.Msg);
						console.log(result);
				}
			}
		}
	}

	$(function() {
		$("#userResetPasswordForm").submit(function() {
			resetPasswordAction($('#email').val());
			return false;
		});

		$("#btnToLogin").click(function() {
			Launcher.goTo("LoginForm?locale=<%= __req.query.locale %>");
		});

		$(window).on("load", function() {
			Launcher.loaded(320, 570);
		});
	});
</script>
</head>
<body oncontextmenu="return false" ondragstart="return false" onselectstart="return false" style="overflow: hidden;">
<div class="wrap">
	<div class="msg-modal" id="msg-modal"><span></span></div>
<%_ if (!captcha) { _%>
	<form class="form-horizontal" name="userResetPasswordForm" method="post" action="" id="userResetPasswordForm" style="padding-top: 20px; display: none;">
<%_ } else { _%>
	<form class="form-horizontal" name="userResetPasswordForm" method="post" action="" id="userResetPasswordForm" style="display: none;">
<%_ } _%>
		<div class="form-group" style="padding-top: 30px;">
			<div class="col">
				<input type="text" class="form-control form-email" id="email" name="email" value="" placeholder="<%= __('Enter email or login') %>" maxlength="128" tabindex="2">
			</div>
		</div>
<%_ if (captcha) { _%>
		<div class="form-group" style="padding-top: 10px; padding-bottom: 5px;">
			<%- captcha %>
		</div>
<%_ } _%>
		<div class="form-group" style="padding-top: 40px;">
			<div class="col" style="text-align: center;">
				<button type="submit" tabindex="3" class="btn-blue"><%= __('Reset Password') %></button>
				<br>
				<a href="#" style="text-decoration: underline;" id="btnToLogin"><%= __('Login to another account') %></a>
			</div>
		</div>
	</form>
	<button class="btn-close" onclick="Launcher.sendCommand('close');">Close</button>
</div>
<!--/wrap-->
</body>
</html>
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge;">
<title>Launcher</title>
<link rel="stylesheet" href="/public/launcher/css/core.css">
<link rel="stylesheet" href="/public/launcher/css/launcher-update-modal.css">
<script type="text/javascript" src="/public/launcher/js/jquery-1.11.1.min.js"></script>
<script>
	var startFileChekingString = "<%= __('Start files checking?') %>";
	var accountBlockedString = "<%= __('Account blocked. Please contact support.') %>";
	var serverMaintenanceString = "<%= __('The server is under maintenance. Please try again later.') %>";

	// 启动器更新相关翻译
	var UPDATE_STRINGS = {
		"loading": "<%= __('Loading...') %>",
		"loadingUpdateInfo": "<%= __('Loading update information...') %>",
		"latestVersion": "<%= __('Latest Version') %>",
		"updating": "<%= __('Updating...') %>",
		"checkingUpdates": "<%= __('Checking for updates...') %>",
		"downloadingFile": "<%= __('Downloading update file...') %>",
		"downloadingProgress": "<%= __('Downloading update file... (%s%)') %>",
		"replacingFiles": "<%= __('Replacing launcher files...') %>",
		"updateCompleted": "<%= __('Update completed! Restarting launcher...') %>",
		"restarting": "<%= __('Restarting launcher...') %>",
		"restartingWait": "<%= __('Restarting, please wait...') %>",
		"upToDate": "<%= __('Already up to date') %>",
		"updateFailed": "<%= __('Update failed: %s') %>",
		"unknownError": "<%= __('Unknown error') %>",
		"updateNow": "<%= __('Update Now') %>",
		"upToDateAlert": "<%= __('Launcher is already up to date!') %>",
		"checkFailed": "<%= __('Update check failed: %s') %>"
	};

	var ACCOUNT_ID = null;
	var PERMISSION = null;
	var PRIVILEGE = null;
	var BANNED = false;
	var REGION = null;

	var QA_MODE = false;
	var QA_PRIVILEGE = "<%= qaPrivilege %>";
	var QA_MODE_NOCHECK = false;

	var PATCH_NO_CHECK = <%= patchNoCheck %>;
	var START_NO_CHECK = <%= startNoCheck %>;
	var PATCH_URL = "<%= patchUrl %>";

	var USER_LANG = (navigator.language || navigator.userLanguage).split('-')[0];

	var REGIONS = {
		<%_ localizations.forEach(localization => { _%>
		"<%= localization.region %>": "<%= localization.language %>",
		<%_ }) _%>
	};

	var ACTS_MAP = {
		<%_ Object.entries(actsMap).forEach(([key, value]) => { _%>
		<%= key %>: <%- value.startsWith('/') ? `location.protocol + "//${host + value}"` : `"${value}"` %>,
		<%_ }) _%>
	};

	var PAGES_MAP = {
		<%_ Object.entries(pagesMap).forEach(([key, value]) => { _%>
		<%= key %>: <%- value.startsWith('/') ? `location.protocol + "//${host + value}"` : `"${value}"` %>,
		<%_ }) _%>
	};

	var PATCH_INFO_STRINGS = {
		"0": "<%= __('Preparing...') %>",
		"1": "<%= __('Download') %>",
		"2": "<%= __('Unpack') %>",
		"3": "<%= __('Patch') %>",
		"4": "<%= __('Check') %>",
		"5": "<%= __('Delete') %>",
		"6": "<%= __('Check') %>",
		"11": "<%= __('Making patch list...') %>"
	};

	var GAMESTART_BUTTON_STRINGS = {
		"btn-gamestart": "<%= __('Play') %>",
		"btn-wrong": "<%= __('Error') %>",
		"btn-wait": "<%= __('Wait') %>",
		"btn-update": "<%= __('Update') %>",
		"btn-break": "<%= __('Abort') %>",
		"btn-repair": "<%= __('Repair') %>"
	}

	document.onkeydown = function() {
		if (event.keyCode == 116) {
			event.keyCode = 2;
			return false;
		} else if (event.ctrlKey && (event.keyCode == 78 || event.keyCode == 82)) {
			return false;
		}
	};
</script>
<script type="text/javascript" src="/public/launcher/js/launcher-util.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher-errors.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher.js"></script>
<script type="text/javascript" src="/public/launcher/js/launcher-update-modal.js"></script>
<script>
	function setQaBox() {
		$("#qaRegion option[value=" + REGION + "]").attr("selected", "selected");
		$("#qaBox").show();

		$("#qaHide").click(function() {
			$("#qaBox a, #qaBox fieldset").hide();
			$("#qaBox").fadeOut("fast");
		});

		$("#qaRegion").on("change", function() {
			Launcher.abortPatch();
			Launcher.setRegion(this.value, true);
		});

		$("#qaModeCheck").click(function() {
			QA_MODE_NOCHECK = $(this).is(":checked");
		});

		QA_MODE_NOCHECK = $("#qaModeCheck").is(":checked");
	};



	$(function() {
		Launcher.login();

		if (QA_MODE) {
			setQaBox();
		}

		$(".region").click(function() {
			Launcher.abortPatch();
			Launcher.setRegion($(this).data("region"), true);
			return false;
		});

		$("#btn-logout").click(function() {
			Launcher.abortPatch();
			Launcher.goTo("LogoutAction", true);
			return false;
		});
	});

	$(window).on("load", function() {
		Launcher.loaded(960, 610);
		
		// 启动器加载完成后，延迟检查更新
		setTimeout(function() {
			checkLauncherUpdateOnStart();
		}, 2000);
	});
</script>
<style type="text/css">
	html { overflow: hidden; padding: 0; margin: 0; }
</style>
</head>
<body oncontextmenu="return false" ondragstart="return false" onselectstart="return false">
	<div class="wrap" style="display: none;" id="launcherMain">
		<div class="msg-modal" id="msg-modal">
			<div class="close-btn">&#10005;</div>
			<h1 id="msg-modal-title"><%= __("Error") %>:</h1>
			<span></span>
		</div>
		
		<!-- 启动器更新模态窗口 -->
		<%- include('./partials/launcherUpdateModal.ejs') %>
		<div class="header">
			<div class="logo"><img src="/public/launcher/images/logo.png"></div>
			<!--/logo-->
			<div class="col">
				<%_ if (localeSelector && localizations.length > 1) { _%>
				<ul class="nav" style="clear: both;">
					<%_ localizations.forEach(localization => { _%>
					<%_ if (localization.language == language) { _%>
						<li><span><img src="/public/launcher/images/flags/<%= localization.language %>.png"> <%= localization.name %></span></li>
					<%_ } else { _%>
						<li><a href="#" class="region" data-region="<%= localization.region %>"><img src="/public/launcher/images/flags/<%= localization.language %>.png"> <%= localization.name %></a></li>
					<% } %>
					<% }) %>
				</ul>
				<% } %>
			</div>
			<!--/col-->
		</div>
		<!--/header-->
		<div class="content">
			<div class="notice">
				<h3><%= __("Welcome") %>, <%= user.userName %>! <a href="#" class="btn-logout" id="btn-logout"></a></h3>
				<div id="qaBox" class="qa-box" style="display: none;">
					<fieldset>
						<legend><%= __("QA Operations Menu") %></legend>
						<table>
							<tr><td><%= __("Disable file check") %></td><td><input type="checkbox" id="qaModeCheck" checked></td></tr>
							<tr><td><%= __("Client language") %></td><td><select id="qaRegion">
								<%_ localizations.forEach(localization => { _%>
								<option value="<%= localization.region %>"><%= localization.name %></option>
							<% }) %>
							</select></td></tr>
						</table>
					</fieldset>
					<a href="#" id="qaHide" class="qa-hide"><%= __("Hide block") %></a>
				</div>
			</div>
			<!--/notice-->
			<div class="ad"></div>
			<!--/ad-->
			<div class="bottom">
				<div class="progress">
					<h3></h3>
					<div class="total">
						<div class="tit"><%= __("total") %></div>
						<div class="progress-bar progressAll">
							<div class="progress-bar-complete" id="progressBar2" style="width: 30%;"></div>
							<span class="file-name" id="totalText"></span>
						</div>
					</div>
					<div class="now">
						<div class="tit"><%= __("now") %></div>
						<div class="progress-bar progressNow">
							<div class="progress-bar-complete" id="progressBar1" style="width: 80%;"></div>
							<span class="file-name" id="fileName"></span>
						</div>
					</div>&nbsp;
				</div>
				<!--/progress--> 
				<div class="check">
					<%_ if (!/^true$/i.test(patchNoCheck)) { _%>
					<!-- 直接开始文件检查，不再弹出确认框 -->
					<button class="btn-check" onclick="Launcher.filesCheck();" data-tooltip="点击修复按钮将进行全文件检查，并修复客户端所需文件"><%= __('Repair') %></button>&nbsp;
					<%_ } _%>
				</div>
				<!--/check-->
				<div class="dps-plugin">
					<button class="btn-dps" id="dpsButton" onclick="Launcher.launchDpsPlugin();"><%= __('DPS Plugin') %></button>
				</div>
				<!--/dps-plugin-->
				<div class="gamestart">
					<button class="btn-gamestart btn btn-wait" id="playButton" onclick="Launcher.onButtonClick();"><%= __('Wait') %></button>
				</div>
				<!--/gamestart-->
			</div>
			<!--/bottom-->
		</div>
		<!--/content-->
		<div class="footer">&copy; <%= new Date().getFullYear() %> <%= brandName %>. All Rights Reserved.</div>
		<!--/footer-->
		<button class="btn-close" onclick="Launcher.sendCommand('close');">Close</button>
	</div>
	<!--/wrap-->
</body>
</html>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Item Claim Boxes") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-success" title="" href="/boxes/add"><%= __("Add new box") %></a>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("ID") %></th>
											<th></th>
											<th><%= __("Title") %></th>
											<th><%= __("Description") %></th>
											<th><%= __("Days") %></th>
											<th><%= __("Items") %> (<%= __("count") %>)</th>
											<th></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

boxes.forEach((box, id) => {
	const items = [];

	box.items.forEach(item => {
		items.push(
			'<div class="form-control boxed mb-1" style="overflow: hidden; background: transparent !important;">' +
			'<div class="item-icon-block" style="margin: -4px 10px 0 -10px;">' +
				`<img src="/static/images/tera-icons/${item.icon}.png" class="item-icon-grade-${item.rareGrade} item-icon"> ` +
				`<img src="/static/images/icons/icon_grade_${item.rareGrade}.png" class="item-icon-grade" alt="">` +
			'</div>' +
			`<small class="item-grade-${item.rareGrade}">${item.string || __("[unknown]")} (${item.boxItemCount})</small>` +
			'</div>'
		);
	});

	tableData.push([
		id,
		`<div class="mw-10 item-icon-block"><img src="/static/images/icons/${box.icon.toLowerCase()}.png" class="item-icon" alt=""></div>`,
		`<a href="/boxes/edit?id=${id}">${box.title}</a>`,
		box.content,
		box.days,
		items.join(""),
		(!box.processing ?
			`<a class="btn btn-secondary" href="/boxes/send?id=${id}"><i class="fa fa-arrow-right"></i><i class="fa fa-user"></i></a> ` +
			`<a class="btn btn-secondary" href="/boxes/send_all?id=${id}"><i class="fa fa-arrow-right"></i><i class="fa fa-users"></i></a> ` 
		:
			`<a class="btn btn-warning-outline btn-sm" href="/tasks?handler=createBox&amp;tag=${id}"><i class="fa fa-tasks"></i> <i class="fa fa-refresh"></i></a> `
		) +
			`<a class="btn btn-secondary btn-sm ml-2" href="/boxes/edit?id=${id}"><i class="fa fa-pencil"></i></a> ` +
			`<a class="btn btn-secondary btn-sm" href="/boxes/delete?id=${id}" data-toggle="modal" data-target="#confirm-del-modal"><i class="fa fa-trash-o "></i></a>` +
		'</td>'
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columnDefs: [{ orderable: false, targets: [1, -2, -1] }],
			columns: [null, { width: "1%" }, null, null, null, null, { className: "text-right text-nowrap" }]
		}));
		$(".section").show();
	});
</script>
<%_
const reportType = {
	1: __("Create"),
	2: __("Edit"),
	3: __("Delete")
};
const genderId = [
	__("Male"),
	__("Female")
];
const raceId = [
	__("Human"),
	__("High Elf"),
	__("<PERSON><PERSON>"),
	__("Castani<PERSON>"),
	__("<PERSON><PERSON>"),
	__("Baraka"),
	__("Elin")
];
const classId = [
	__("Warrior"),
	__("Lancer"),
	__("Slayer"),
	__("<PERSON><PERSON><PERSON><PERSON>"),
	__("Sorcerer"),
	__("<PERSON>"),
	__("<PERSON>"),
	__("Mystic"),
	__("Reaper"),
	__("<PERSON>"),
	__("Brawler"),
	__("Ninja"),
	__("Valkyrie")
];
_%>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Characters Report") %></h1>
	</div>
	<%- include("partials/adminReportForm", { servers, from, to, serverId, accountDBID }) -%>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Report time") %></th>
											<th><%= __("Action") %></th>
											<th><%= __("ID") %></th>
											<th><%= __("Name") %></th>
											<th><%= __("Level") %></th>
											<th><%= __("Race") %></th>
											<th><%= __("Gender") %></th>
											<th><%= __("Class") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Server ID") %></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

reports.forEach(report => {
	tableData.push([
			moment(report.get("reportTime")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss"),
			reportType[report.get("reportType")],
			report.get("characterId"),
			report.get("name") || "-",
			report.get("level") || "-",
			raceId[report.get("raceId") == 4 && report.get("genderId") == 1 ? 6 : report.get("raceId")] || "-",
			genderId[report.get("genderId")] || "-",
			classId[report.get("classId")] || "-",
			report.get("accountDBID"),
			report.get("account")?.get("userName") || "-",
			`(${report.get("serverId")}) ${report.get("server")?.get("nameString") || ""}`
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]]
		}));
		$(".section").show();
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Add Account") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Account Information") %></h3>
						</div>
						<div class="form-group">
							<label class="control-label" for="userName"><%= __("User name") %></label>
							<input type="text" class="form-control boxed" name="userName" value="" minlength="3" maxlength="24">
						</div>
						<div class="form-group">
							<label class="control-label" for="passWord"><%= __("Password") %></label>
							<input type="text" class="form-control boxed" name="passWord" value="" minlength="8" maxlength="128">
						</div>
						<div class="form-group">
							<label class="control-label" for="email"><%= __("Email") %></label>
							<input type="email" class="form-control boxed" name="email" value="">
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="permission"><%= __("Permission") %></label>
								<input type="number" class="form-control boxed" name="permission" value="0" min="0" max="***********">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="privilege"><%= __("Privilege") %></label>
								<input type="number" class="form-control boxed" name="privilege" value="0" min="0" max="***********">
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label>
									<button type="button" class="btn btn-secondary" id="permission-priority"><i class="fa fa-level-up"></i> <%= __("Set PL") %></button>
								</label>
								<label>
									<button type="button" class="btn btn-secondary" id="permission-maintainer"><i class="fa fa-wrench"></i> <%= __("Set MT") %></button>
								</label>
								<label>
									<button type="button" class="btn btn-secondary" id="permission-reset"><i class="fa fa-refresh"></i> <%= __("Reset") %></button>
								</label>
							</div>
							<div class="col-sm-6">
								<label>
									<button type="button" class="btn btn-secondary" id="privilege-gm"><i class="fa fa-magic"></i> <%= __("Set GM") %></button>
								</label>
								<label>
									<button type="button" class="btn btn-secondary" id="privilege-reset"><i class="fa fa-refresh"></i> <%= __("Reset") %></button>
								</label>
							</div>
						</div>
						<div class="title-block">
							<h3 class="title"><%= __("Account Benefits") %></h3>
						</div>
						<div id="benefits">
							<%_ benefitIds.forEach((benefitId, i) => { if (benefitId) { _%>
							<div class="form-group row">
								<div class="col-sm-6">
									<label class="control-label" for="benefitIds[]">
										<%= __("Benefit ID") %>
										<a class="btn btn-secondary btn-sm remove-benefit" title="" href="#"><i class="fa fa-minus"></i></a>
									</label>
									<select class="form-control boxed" name="benefitIds[]">
										<option value="">- <%= __("None") %> -</option>
									<%_ Array.from(accountBenefits.getAll()).forEach(({ string, id }) => { _%>
										<option value="<%= id %>" <%= benefitId == id ? 'selected' : "" %>>(<%= id %>) <%= string %></option>
									<%_ }) _%>
									</select>
								</div>
								<div class="col-sm-6">
									<label class="control-label" for="availableUntils[]"><%= __("Available until") %></label>
									<input type="datetime-local" class="form-control boxed mt-2" name="availableUntils[]" value="<%= moment(availableUntils[i]).format("YYYY-MM-DDTHH:mm") %>">
								</div>
							</div>
							<%_ } }) _%>
						</div>
						<div class="form-group">
							<button type="button" class="btn btn-secondary" id="add-benefit"><i class="fa fa-plus"></i> <%= __("Add benefit") %></button>
						</div>
						<div class="form-group row"></div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Add") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#permission-priority").click(function() {
			$("input[name='permission']").val("1");
		});
		$("#permission-maintainer").click(function() {
			$("input[name='permission']").val("256");
		});
		$("#permission-reset").click(function() {
			$("input[name='permission']").val("0");
		});
		$("#privilege-gm").click(function() {
			$("input[name='privilege']").val("33");
		});
		$("#privilege-reset").click(function() {
			$("input[name='privilege']").val("0");
		});
		$(".remove-benefit").click(function() {
			$(this).closest(".row").remove();
		});
		$("#add-benefit").click(function() {
			$("#benefits").append(
				"<div class='form-group row'>" +
				"	<div class='col-sm-6'>" +
				"		<label class='control-label' for='benefitIds[]'>" +
				"			<%= __('Benefit ID') %>" +
				"			<a class='btn btn-secondary btn-sm remove-benefit' title='' href='#''><i class='fa fa-minus'></i></a></label>" +
				"		<select class='form-control boxed' name='benefitIds[]'>" +
				"			<option value=''>- <%= __('None') %> -</option>" +
				"			<% Array.from(accountBenefits.getAll()).forEach(({ string, id }) => { %><option value='<%= id %>'>(<%= id %>) <%= string %></option><% }) %>" +
				"		</select>" +
				"	</div>" +
				"	<div class='col-sm-6'>" +
				"		<label class='control-label' for='availableUntils[]'><%= __('Available until') %></label>" +
				"		<input type='datetime-local' class='form-control boxed mt-2' name='availableUntils[]' value='<%= moment().subtract(30, 'days').tz(user.tz).format('YYYY-MM-DDTHH:mm') %>'>" +
				"	</div>" +
				"</div>"
			);
			$(".remove-benefit").click(function() {
				$(this).closest(".row").remove();
			});
		});
		$("#form").validate(config.validations);
	});
</script>
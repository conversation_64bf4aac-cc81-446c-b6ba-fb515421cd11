<%- contentFor("content") %>
<article class="content dashboard-page">
	<section class="section">
		<div class="row">
			<div class="col-md-6">
				<section class="section">
					<div class="card stats">
						<div class="card-block">
							<div class="title-block">
								<h4 class="title"><%= __("Servers Stats") %> <span id="isMaintenance" class="text-warning"><%- isMaintenance ? `<i class="fa fa-wrench"></i> ${__("Under maintenance")}` : "" %></span></h4>
							</div>
							<%_ if (servers === null || servers.length === 0) { _%>
							<div class="row stats-col">
								<div class="col-12 col-md-4 stat-col">
									<%= __("No data available in table") %>
								</div>
							</div>
							<%_ } else { _%>
							<%_ servers.forEach(server => { _%>
							<div class="row stats-col">
								<div class="col-6 col-md-4 stat-col">
									<div class="stat-icon <%= server.get("isAvailable") ? "text-success" : "text-danger" %>" id="isAvailable_<%= server.get("serverId") %>">
										<i class="fa fa-desktop"></i>
									</div>
									<div class="stat">
										<div class="value" id="nameString_<%= server.get("serverId") %>"><%= server.get("nameString") %></div>
										<div class="name" id="loginIpPort_<%= server.get("serverId") %>"><%= server.get("loginIp") %>:<%= server.get("loginPort") %></div>
									</div>
								</div>
								<div class="col-6 col-md-4 stat-col">
									<div class="stat-icon">
										<i class="fa fa-users"></i>
									</div>
									<div class="stat">
										<div class="value" id="usersTotal_<%= server.get("serverId") %>"><%= server.get("usersTotal") %></div>
										<div class="name"><%= __("Registered Users") %></div>
									</div>
								</div>
								<div class="col-6 col-md-4 stat-col">
									<div class="stat-icon">
										<i class="fa fa-sitemap"></i>
									</div>
									<div class="stat">
										<div class="value" id="usersOnline_<%= server.get("serverId") %>"><%= server.get("usersOnline") %></div>
										<div class="name"><%= __("Online Users") %></div>
									</div>
									<div class="progress stat-progress">
										<div class="progress-bar" <%- `style="width: ${Math.floor((server.get("usersOnline") / server.get("thresholdMedium")) * 100)}%;"` %> id="usersOnlinePercent_<%= server.get("serverId") %>"></div>
									</div>
								</div>
							</div>
							<%_ }) _%>
							<%_ } _%>
						</div>
					</div>
				</section><!--/stats-->
				<%_ if (activityReport !== null) { _%>
				<section class="section">
					<div class="card">
						<div class="card-block">
							<div class="title-block">
								<h4 class="title"><%= __("Activity Report") %> <a class="ml-2 btn btn-sm btn-secondary" href="/report_activity"><%= __("Show full report") %></a></h4>
							</div>
							<div class="table-responsive">
								<table class="table">
									<thead>
										<tr>
											<th><%= __("Report time") %></th>
											<th><%= __("Action (playtime)") %></th>
											<th><%= __("Client IP") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("Server ID") %></th>
										</tr>
									</thead>
									<tbody id="activityReport"></tbody>
								</table>
							</div>
						</div>
					</div>
				</section><!--/report-->
				<%_ } _%>
			</div> <!--/col-->
			<div class="col-md-6">
				<%_ if (payLogs !== null) { _%>
				<section class="section">
					<div class="card">
						<div class="card-block">
							<div class="title-block">
								<h4 class="title"><%= __("Shop Pay Logs") %> <a class="ml-2 btn btn-sm btn-secondary" href="/shop_pay_logs"><%= __("Show full log") %></a></h4>
							</div>
							<div class="table-responsive">
								<table class="table">
									<thead>
										<tr>
											<th><%= __("Log time") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("Server ID") %></th>
											<th><%= __("Product ID") %></th>
											<th><%= __("Amount") %></th>
											<th><%= __("Status") %></th>
										</tr>
									</thead>
									<tbody id="payLogs"></tbody>
								</table>
							</div>
						</div>
					</div>
				</section><!--/report-->
				<%_ } _%>
			</div> <!--/col-->
		</div> <!--/row-->
	</section>
	<section class="section">
		<div class="row">
			<div class="col-md-12">
				<%_ if (cheatsReport !== null) { _%>
				<section class="section">
					<div class="card">
						<div class="card-block">
							<div class="title-block">
								<h4 class="title"><%= __("Cheats Report") %> <a class="ml-2 btn btn-sm btn-secondary" href="/report_cheats"><%= __("Show full report") %></a></h4>
							</div>
							<div class="table-responsive">
								<table class="table">
									<thead>
										<tr>
											<th><%= __("Report time") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Character") %></th>
											<th><%= __("Type") %></th>
											<th><%= __("Skill") %></th>
											<th><%= __("Hits") %>/<%= __("Limit") %></th>
											<th><%= __("Target") %></th>
											<th><%= __("Client IP") %></th>
											<th><%= __("Server ID") %></th>
										</tr>
									</thead>
									<tbody id="cheatsReport"></tbody>
								</table>
							</div>
						</div>
					</div>
				</section><!--/report-->
				<%_ } _%>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		function loadHomeStats() {
			$.ajax({
				url: "/api/homeStats",
				method: "get",
				async: false,
				success: function(data) {
					if (data.result_code !== 0) {
						return;
					}
					if (data.isMaintenance) {
						$("#isMaintenance").html("<i class=\"fa fa-wrench\"></i> <%= __("Under maintenance") %>");
					} else {
						$("#isMaintenance").empty();
					}
					data.servers.forEach(function(server) {
						if (server.isAvailable) {
							$("#isAvailable_" + server.serverId).removeClass("text-danger").addClass("text-success");
						} else {
							$("#isAvailable_" + server.serverId).removeClass("text-success").addClass("text-danger")
						}
						$("#nameString_" + server.serverId).html(server.nameString);
						$("#loginIpPort_" + server.serverId).html(server.loginIp + ":" + server.loginPort);
						$("#usersTotal_" + server.serverId).html(server.usersTotal);
						$("#usersOnline_" + server.serverId).html(server.usersOnline);
						$("#usersOnlinePercent_" + server.serverId).css("width", server.usersOnlinePercent + "%");
					});
					$("#activityReport").empty();
					if (data.activityReport.length === 0) {
						$("#activityReport").html("<tr><td colspan=\"5\" class=\"text-center\"><%= __("No data available in table") %></td></tr>");
					} else {
						data.activityReport.forEach(report => {
							$("#activityReport").append(
								"<tr>" +
									"<td class=\"text-nowrap\">" + report.reportTime + "</td>" +
									"<td>" + report.reportType + "</td>" +
									"<td>" + (report.ip.length > 15 ? report.ip.substring(0, 15) + '...' : report.ip) + "</td>" +
									"<td><a href=\"/accounts?accountDBID=" + report.accountDBID + "\">" + report.accountDBID + "</a></td>" +
									"<td>" + report.serverId + "</td>" +
								"</tr>"
							);
						});
					}
					$("#payLogs").empty();
					if (data.payLogs.length === 0) {
						$("#payLogs").html("<tr><td colspan=\"6\" class=\"text-center\"><%= __("No data available in table") %></td></tr>");
					} else {
						data.payLogs.forEach(report => {
							$("#payLogs").append(
								"<tr>" +
									"<td class=\"text-nowrap\">" + report.createdAt + "</td>" +
									"<td><a href=\"/accounts?accountDBID=" + report.accountDBID + "\">" + report.accountDBID + "</a></td>" +
									"<td>" + report.serverId + "</td>" +
									"<td><a href=\"/shop_products/edit?id=" + report.productId + "\">" + report.productId + "</a></td>" +
									"<td>" + report.amount + "</td>" +
									"<td class=\"text-" + (report.status == "completed" ? "success" : (report.status == "deposit" ? "primary" : "danger")) + "\">" + report.statusString + "</td>" +
								"</tr>"
							);
						});
					}
					$("#cheatsReport").empty();
					if (data.cheatsReport.length === 0) {
						$("#cheatsReport").html("<tr><td colspan=\"10\" class=\"text-center\"><%= __("No data available in table") %></td></tr>");
					} else {
						data.cheatsReport.forEach(report => {
							$("#cheatsReport").append(
								"<tr>" +
									"<td class=\"text-nowrap\">" + report.reportTime + "</td>" +
									"<td><a href=\"/accounts?accountDBID=" + report.accountDBID + "\">" + report.accountDBID + "</a></td>" +
									"<td>" + report.userName + "</td>" +
									"<td>" + report.name + "</td>" +
									"<td>" + report.type + "</td>" +
									"<td>" + report.skill + "</td>" +
									"<td>" + report.hits + "/" + report.limit + "</td>" +
									"<td>" + report.dungeon + "<br>" + report.creature + "<br>" + report.coords + "</td>" +
									"<td>" + report.ip + "</td>" +
									"<td>" + report.serverId + "</td>" +
								"</tr>"
							);
						});
					}
				}
			});
		}
		loadHomeStats();
		setInterval(loadHomeStats, 10000);
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Add Shop Slide") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form" enctype="multipart/form-data">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="displayDateStart"><%= __("Display start date") %></label>
								<input type="datetime-local" class="form-control boxed" name="displayDateStart" value="<%= moment().tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="displayDateEnd"><%= __("Display end date") %></label>
								<input type="datetime-local" class="form-control boxed" name="displayDateEnd" value="<%= moment().add(365, "days").tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
						</div>
						<div class="form-group">
							<label>
								<input type="checkbox" class="checkbox" name="active" checked="checked">
								<span><%= __("Active") %></span>
							</label>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<div>
									<label class="control-label" for="image"><%= __("Background image") %></label>
									<select class="form-control boxed" name="image" id="image">
										<option value="">- <%= __("Upload new") %> -</option>
									<%_ images.forEach(({ name, used}) => { _%>
										<option value="<%= name %>" data-used="<%= used %>"><%= name %><%= !used ? ` (${__("not used")})` : "" %></option>
									<%_ }) _%>
									</select>
								</div>
								<div class="mt-3">
									<label class="control-label" for="productId"><%= __("Product ID") %></label>
									<input type="number" class="form-control boxed" name="productId" value="" min="1" max="100000000">
								</div>
								<div class="mt-3">
									<label class="control-label" for="priority"><%= __("Priority") %></label>
									<input type="number" class="form-control boxed" name="priority" value="0" min="-100000000" max="100000000">
								</div>
							</div>
							<div class="col-sm-6">
								<div id="image-preview-form" style="display: none;">
									<label class="control-label" for="imagePreview"><%= __("Preview") %></label>
									<div><img id="imagePreview" src="" width="350" style="display: none;"></div>
									<button type="button" class="btn btn-danger-outline mt-3" id="remove-image" data-toggle="modal" data-target="#confirm-del-modal" style="display: none;"><i class="fa fa-trash"></i> <%= __("Remove image") %></button>
								</div>
								<div id="image-upload-form" style="display: none;">
									<label class="control-label" for="imageFile"><%= __("File upload") %></label>
									<input type="file" class="mb-2" id="imageFile" name="imageFile" accept="image/png, image/jpeg">
									<div><img class="mb-2" id="imageFilePreview" src="" width="350" class="mt-3" style="display: none;"></div>
									<dfn><%= __("Required resolution: %sx%sx", SLIDE_WIDTH, SLIDE_HEIGHT) %></dfn>
								</div>
							</div>
						</div>
						<div class="form-group row"></div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		var validator = $("#form").validate(config.validations);
		function updateImage() {
			var selectedImage = $("#image").val();
			if (selectedImage) {
				if ($("#image").find(':selected').data("used")) {
					$("#remove-image").hide();
				} else {
					$("#remove-image").show();
				}
				$("#image-upload-form").hide();
				$("#image-preview-form").show();
				$("#imagePreview").attr("src", "/static/images/shop-slides-bg/" + selectedImage).show();
			} else {
				$("#image-preview-form").hide();
				$("#image-upload-form").show();
				$("#imagePreview").attr("src", "").hide();
			}
		}
		updateImage();
		$("#image").change(function() {
			updateImage();
		});
		$("#remove-image").on("confirmedAction", function() {
			var selectedImage = $("#image").val();
			$.ajax({
				type: "GET",
				url: location + "?removeImage=" + selectedImage,
				success: function(reply) {
					if (reply.result_code === 0) {
						$("#image option[value='" + selectedImage + "']").remove();
						$("#image-preview-form").hide();
						$("#image-upload-form").show();
						$("#imagePreview").attr("src", "").show();
					} else if (reply.result_code === 1000) {
						validator.showErrors({ image: "<%= __('This image is used in the slide.') %>" });
					} else {
						validator.showErrors({ image: "<%= __('Error deleting image.') %>" });
					}
				}
			});
		});
		$("#imageFile").change(function() {
			var allowedTypes = ["image/jpeg", "image/png"];
			var maxSize = <%= MAX_SLIDE_SIZE * 1024 * 1024 %>;
			var width = <%= SLIDE_WIDTH %>;
			var height = <%= SLIDE_HEIGHT %>;
			var [file] = this.files;
			function showError(message) {
				$("#imageFile").val("");
				validator.showErrors({ imageFile: message });
			}
			if (file) {
				if (!allowedTypes.includes(file.type)) {
					showError("<%= __('Only JPG and PNG files are allowed') %>");
					return;
				}
				if (file.size > maxSize) {
					showError("<%= __('The file is too big! Max size: %s MB', MAX_SLIDE_SIZE) %>");
					return;
				}
				var img = new Image();
				img.onload = function() {
					if (this.width !== width || this.height !== height) {
						showError("<%= __('The resolution must be: %sx%s', SLIDE_WIDTH, SLIDE_HEIGHT) %>");
					} else {
						$("#imageFilePreview").attr("src", URL.createObjectURL(file)).show();
					}
				};
				img.src = URL.createObjectURL(file);
			} else {
				$("#imageFilePreview").hide();
			}
		});
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Item Claim Boxes Logs") %></h1>
	</div>
	<%- include("partials/adminReportForm", { servers, from, to, serverId, accountDBID }) -%>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<div class="table-responsive">
								<table class="table table-hover data-table-json mt-5">
									<thead>
										<tr>
											<th><%= __("Log time") %></th>
											<th><%= __("Box ID") %></th>
											<th><%= __("Type") %></th>
											<th><%= __("Log ID") %></th>
											<th><%= __("Account ID") %></th>
											<th><%= __("User name") %></th>
											<th><%= __("Server ID") %></th>
											<th><%= __("Character ID") %></th>
											<th><%= __("Title") %></th>
											<th><%= __("Days") %></th>
											<th><%= __("Item template ID") %></th>
										</tr>
									</thead>
								</table>
							</div>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

logs.forEach(log => {
	const context = JSON.parse(log.get("context"));
	let logId = log.get("logId");
	let logType = log.get("logType");

	switch (logType) {
		case 1:
			logType = __("Chronoscroll");
			break;
		case 2:
			logType = __("Promo code");
			logId = `<a href="/promocodes/edit?promoCodeId=${log.get("logId")}">${log.get("logId")}</a>`;
			break;
		case 3:
			logType = __("Shop");
			logId = `<a href="/shop_pay_logs?id=${log.get("logId")}">${log.get("logId")}</a>`;
			break;
		case 4:
			logType = __("Box");
			logId = `<a href="/boxes/edit?id=${log.get("logId")}">${log.get("logId")}</a>`;
			break;
	}

	tableData.push([
		moment(log.get("createdAt")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss"),
		log.get("boxId") || "-",
		logType || "-",
		logId || "-",
		log.get("accountDBID"), 
		log.get("account")?.get("userName") || "-",
		log.get("serverId") ? `(${log.get("serverId")}) ${log.get("server")?.get("nameString") || ""}` : __("All"),
		log.get("characterId") || __("All"),
		context.title,
		context.days,
		context.items.map(item => item.item_template_id ? `${item.item_template_id} (${item.item_count})` : null).join(", ") || "-"
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]],
			columns: [{ className: "text-nowrap" }, null, { className: "text-nowrap" }, null, null, null, { className: "text-nowrap" }, null, { className: "text-nowrap" }, null, null]
		}));
		$(".section").show();
	});
</script>
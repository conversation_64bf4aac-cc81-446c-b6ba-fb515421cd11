<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Activate Promo Code") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group">
							<label class="control-label" for="accountDBID"><%= __("Account ID") %></label>
							<input type="text" class="form-control boxed" name="accountDBID" value="<%= accountDBID %>">
						</div>
						<div class="form-group">
							<label class="control-label" for="promoCodeId"><%= __("Promo code ID") %></label>
							<select class="form-control boxed" name="promoCodeId">
							<%_ promocodes.forEach(promocode => { _%>
								<option value="<%= promocode.get("promoCodeId") %>" <%= promoCodeId == promocode.get("promoCodeId") ? 'selected' : "" %>>(<%= promocode.get("promoCodeId") %>) <%= promocode.get("promoCode") %></option>
							<%_ }) _%>
							</select>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Activate") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Shop Category") %> ID <%= id %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="sort"><%= __("Sort") %></label>
								<input type="number" class="form-control boxed" name="sort" value="<%= sort %>" min="-100000000" max="100000000">
							</div>
						</div>
						<div class="form-group">
							<label>
								<input type="checkbox" class="checkbox" name="active" <%- active ? "checked=\"checked\"" : "" %>>
								<span><%= __("Active") %></span>
							</label>
						</div>
						<div class="title-block">
							<h3 class="title"><%= __("Category Title") %></h3>
						</div>
						<%_ languages.forEach(language => { _%>
						<div class="form-group row">
							<div class="col-sm-2 mt-2">
								<label for="title[<%= language %>]" class="control-label"><%= __(language) %></label>
							</div>
							<div class="col-sm-10">
								<input type="text" class="form-control boxed" name="title[<%= language %>]" value="<%= title[language] || "" %>" minlength="1" maxlength="1024">
							</div>
						</div>
						<%_ }) _%>
						<div class="form-group row"></div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
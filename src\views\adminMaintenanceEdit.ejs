<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Server Maintenance Plan") %> ID <%= id %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="description"><%= __("Start time") %></label>
								<input type="datetime-local" class="form-control boxed" name="startTime" value="<%= startTime.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="description"><%= __("End time") %></label>
								<input type="datetime-local" class="form-control boxed" name="endTime" value="<%= endTime.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label" for="description"><%= __("Description") %></label>
							<textarea type="text" class="form-control boxed" name="description" placeholder="<%= __("optional") %>" minlength="1" maxlength="1024"><%= description %></textarea>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
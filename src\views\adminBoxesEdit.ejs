<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Item Claim Box") %> ID <%= id %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Box Information") %></h3>
						</div>
						<div class="form-group">
							<label class="control-label" for="title"><%= __("Title") %></label>
							<input type="text" class="form-control boxed" name="title" value="<%= title %>" minlength="1" maxlength="1024">
						</div>
						<div class="form-group">
							<label class="control-label" for="content"><%= __("Description") %></label>
							<textarea type="text" class="form-control boxed" name="content" minlength="1" maxlength="2048"><%= content %></textarea>
						</div>
						<div class="form-group row">
							<div class="col-sm-7">
								<label class="control-label" for="days"><%= __("Days") %></label>
								<input type="number" class="form-control boxed" name="days" value="<%= days %>" min="1" max="4000">
							</div>
							<div class="col-sm-5">
								<label class="control-label" for="icon"><%= __("Icon") %></label>
								<div>
									<label>
										<input class="radio squared" name="icon" value="GiftBox01.bmp" type="radio"<%= icon === "GiftBox01.bmp" ? " checked" : "" %>>
										<span><img src="/static/images/icons/giftbox01.bmp.png" class="item-icon-form"></span>
									</label>
									<label>
										<input class="radio squared" name="icon" value="GiftBox02.bmp" type="radio"<%= icon === "GiftBox02.bmp" ? " checked" : "" %>>
										<span><img src="/static/images/icons/giftbox02.bmp.png" class="item-icon-form"></span>
									</label>
								</div>
							</div>
						</div>
						<div class="form-group row"></div>
						<div class="title-block">
							<h3 class="title"><%= __("Box Items") %></h3>
						</div>
						<div id="items">
							<%_ itemTemplateIds.forEach((itemTemplateId, i) => { _%>
							<div class="form-group row">
								<div class="col-sm-7">
									<label class="control-label" for="itemTemplateIds[]">
										<%= __("Item template ID") %>
										<a class="btn btn-secondary btn-sm remove-item" title="" href="#"><i class="fa fa-minus"></i></a>
									</label>
									<input type="text" class="form-control boxed" name="itemTemplateIds[]" value="<%= itemTemplateIds[i] %>">
								</div>
								<div class="col-sm-2">
									<label class="control-label" for="boxItemCounts[]"><%= __("Count") %></label>
									<input type="number" class="form-control boxed mt-2" name="boxItemCounts[]" value="<%= boxItemCounts[i] %>" min="1" max="1000000">
								</div>
								<div class="col-sm-3">
									<label class="control-label" for="boxItemIds[]"><%= __("Service item ID") %></label>
									<input type="number" class="form-control boxed mt-2" name="boxItemIds[]" value="<%= boxItemIds[i] %>" placeholder="<%= __("optional") %>" min="1" max="100000000">
								</div>
							</div>
							<%_ }) _%>
						</div>
						<div class="form-group">
							<button type="button" class="btn btn-secondary" id="add-item"><i class="fa fa-plus"></i> <%= __("Add item") %></button>
						</div>
						<div class="form-group row"></div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		function onItemAdded() {
			$('input[name^="itemTemplateIds"]').change(function() {
				$(this).closest('.row').find('input[name^="boxItemIds"]').val("");
			});
			$(".remove-item").click(function () {
				$(this).closest(".row").remove();
			});
		}
		onItemAdded();
		$("#add-item").click(function() {
			$("#items").append(
				"<div class='form-group row'>" +
				"	<div class='col-sm-7'>" +
				"		<label class='control-label' for='itemTemplateIds[]'>" +
				"			<%= __('Item template ID') %>" +
				"			<a class='btn btn-secondary btn-sm remove-item' title='' href='#'><i class='fa fa-minus'></i></a>" +
				"		</label>" +
				"		<input type='text' class='form-control boxed' name='itemTemplateIds[]' value=''>" +
				"	</div>" +
				"	<div class='col-sm-2'>" +
				"		<label class='control-label' for='boxItemCounts[]'><%= __('Count') %></label>" +
				"		<input type='number' class='form-control boxed mt-2' name='boxItemCounts[]' value='1' min='1' max='1000000'>" +
				"	</div>" +
				"	<div class='col-sm-3'>" +
				"		<label class='control-label' for='boxItemIds[]'><%= __('Service item ID') %></label>" +
				"		<input type='number' class='form-control boxed mt-2' name='boxItemIds[]' value='' placeholder='<%= __('optional') %>' min='1' max='100000000'>" +
				"	</div>" +
				"</div>"
			);
			onItemAdded();
			addAutocomplete();
		});
		$("#form").validate(config.validations);
	});
</script>
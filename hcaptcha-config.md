# hCaptcha 配置说明

## 1. 获取 hCaptcha 密钥

1. 访问 [hCaptcha 官网](https://www.hcaptcha.com/)
2. 注册账号并登录
3. 在 Dashboard 中创建一个新的 site
4. 获取以下密钥：
   - **Site Key** (公钥，用于前端)
   - **Secret Key** (私钥，用于后端验证)

## 2. 配置环境变量

在你的环境配置文件中添加以下变量：

```bash
# 启用验证码功能
API_PORTAL_CAPTCHA_ENABLE=true

# hCaptcha 配置
API_PORTAL_HCAPTCHA_SITEKEY=your-site-key-here
API_PORTAL_HCAPTCHA_SECRET=your-secret-key-here
```

## 3. 配置说明

- `API_PORTAL_CAPTCHA_ENABLE`: 是否启用验证码功能
- `API_PORTAL_HCAPTCHA_SITEKEY`: hCaptcha 的 Site Key（公钥）
- `API_PORTAL_HCAPTCHA_SECRET`: hCaptcha 的 Secret Key（私钥）

## 4. 测试配置

1. 重启API服务器
2. 访问以下页面验证 hCaptcha 组件：
   - 注册页面
   - 密码重置页面
3. 应该能看到 hCaptcha 验证组件
4. 完成验证后应该能正常提交表单

## 5. 故障排除

### 验证码不显示
- 检查 `API_PORTAL_CAPTCHA_ENABLE` 是否设置为 `true`
- 检查 Site Key 和 Secret Key 是否正确
- 查看浏览器控制台是否有错误信息

### 验证失败
- 检查 Secret Key 是否正确
- 检查服务器日志中的错误信息
- 确保服务器能够访问 hCaptcha 的验证端点

### 前端错误
- 检查 hCaptcha 的 JavaScript 是否正确加载
- 确保回调函数正确定义
- 检查浏览器控制台的错误信息

## 6. 安全建议

- 不要在前端代码中暴露 Secret Key
- 定期更新密钥
- 监控验证码的使用情况和成功率
- 考虑启用 IP 白名单功能

## 7. 完整的验证流程

### 注册流程
1. **注册表单** - 用户填写信息 + hCaptcha 人机验证
2. **邮箱验证** - 用户输入邮箱验证码（仅验证码验证）

### 密码重置流程
1. **重置请求** - 用户输入邮箱 + hCaptcha 人机验证
2. **密码重置确认** - 用户输入新密码和邮箱验证码（仅验证码验证）

这样的验证机制在入口处提供了人机验证防护，有效防止：
- 恶意注册攻击
- 批量密码重置攻击
- 自动化脚本攻击

邮箱验证步骤仅需要验证码确认，避免了重复的人机验证，提供更好的用户体验。

## 8. 相关文档

- [hCaptcha 官方文档](https://docs.hcaptcha.com/)
- [hCaptcha JavaScript API](https://docs.hcaptcha.com/configuration)
- [hCaptcha 服务端验证](https://docs.hcaptcha.com/serverside) 
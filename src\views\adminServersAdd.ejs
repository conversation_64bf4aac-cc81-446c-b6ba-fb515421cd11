<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Add Server") %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<%_ if (isSlsOverridden) { %>
							<!-- div class="form-group alert alert-warning">
								<%= __("The API settings contain a dynamic overriding of the server information.") %>
							</div -->
						<%_ } _%>
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="serverId"><%= __("Server ID") %></label>
								<input type="number" class="form-control boxed" name="serverId" value="" min="1">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="language"><%= __("Language code") %></label>
								<select class="form-control boxed" name="language">
									<%_ availableLanguages.forEach(languageCode => { _%>
									<option value="<%= languageCode %>" <%= languageCode === i18n.getLocale() ? "selected" : "" %>>(<%= languageCode %>) <%= __(languageCode) %></option>
									<%_ }); _%>
								</select>
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="loginIp"><%= __("Login IP") %></label>
								<input type="text" class="form-control boxed" name="loginIp" value="">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="loginPort"><%= __("Login port") %></label>
								<input type="number" class="form-control boxed" name="loginPort" value="7801" min="0" max="65535">
							</div>
						</div>
						<div class="form-group">
							<label class="control-label" for="nameString"><%= __("Name string") %></label>
							<input type="text" class="form-control boxed" name="nameString" value="" minlength="1" maxlength="256">
						</div>
						<div class="form-group">
							<label class="control-label" for="descrString"><%= __("Description string") %></label>
							<input type="text" class="form-control boxed" name="descrString" value="" minlength="1" maxlength="1024">
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="thresholdLow"><%= __("Threshold low") %></label>
								<input type="number" class="form-control boxed" name="thresholdLow" value="100" min="0" max="100000000">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="thresholdMedium"><%= __("Threshold medium") %></label>
								<input type="number" class="form-control boxed" name="thresholdMedium" value="500" min="0" max="100000000">
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="permission"><%= __("Permission") %></label>
								<input type="number" class="form-control boxed" name="permission" value="0" min="0" max="10000000000">
							</div>
						</div>
						<div class="form-group">
							<label>
								<input type="checkbox" class="checkbox" name="isPvE">
								<span><%= __("Only PvE") %></span>
							</label>
							<label>
								<input type="checkbox" class="checkbox" name="isCrowdness">
								<span><%= __("Is crowdness") %></span>
							</label>
							<label>
								<input type="checkbox" class="checkbox" name="isAvailable">
								<span><%= __("Is available") %></span>
							</label>
							<label>
								<input type="checkbox" class="checkbox" name="isEnabled" checked="checked">
								<span><%= __("Is enabled") %></span>
							</label>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Add") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
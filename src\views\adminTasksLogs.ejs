<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Admin Tasks Queue Log") %></h1>
	</div>
	<section class="section">
		<form class="form-inline" action="" method="GET">
			<div class="form-group">
				<label for="from"><%= __("Period from") %></label>
				<input type="datetime-local" class="form-control boxed" name="from" value="<%= from.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
			</div>
			<div class="form-group">
				<label for="to"><%= __("to") %></label>
				<input type="datetime-local" class="form-control boxed" name="to" value="<%= to.tz(user.tz).format("YYYY-MM-DDTHH:mm") %>">
			</div>

			<div class="form-group">
				<label for="taskId"><%= __("Task ID") %></label>
				<input type="text" class="form-control boxed" name="taskId" value="<%= taskId %>" size="15">
			</div>
			<div class="form-group">
				<label for="handler"><%= __("Task name") %></label>
				<input type="text" class="form-control boxed" name="handler" value="<%= handler %>" size="15">
			</div>
			<div class="form-group">
				<label for="tag"><%= __("Task tag") %></label>
				<input type="text" class="form-control boxed" name="tag" value="<%= tag %>" size="15">
			</div>
			<button type="submit" class="btn btn-primary"><%= __("Show") %></button>
		</form>
	</section>
	<section class="section" style="display: none;">
		<div class="row">
			<div class="col-md-12">
				<div class="card">
					<div class="card-block">
						<section>
							<table class="table table-hover data-table-json mt-5">
								<thead>
									<tr>
										<th><%= __("ID") %></th>
										<th><%= __("Report time") %></th>
										<th><%= __("Task ID") %></th>
										<th><%= __("Task name") %></th>
										<th><%= __("Task tag") %></th>
										<th><%= __("Status") %></th>
										<th><%= __("Message") %></th>
									</tr>
								</thead>
							</table>
						</section>
					</div>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<%_
const tableData = [];

reports.forEach(report => {
	tableData.push([
		report.get("id"),
		moment(report.get("reportTime")).tz(user.tz).format("YYYY-MM-DD HH:mm:ss"),
		report.get("taskId"),
		report.get("handler"),
		report.get("tag") || "-",
		`<span class="text-${{ "0": "primary", "1": "warning", "2": "danger", "3": "danger", "4": "success"}[report.get("status")]}">${__({ "0": "Created", "1": "Pending", "2": "Failed", "3": "Cancelled", "4": "Success"}[report.get("status")])}</span>`,
		report.get("message") || "-"
	]);
});
_%>
<script>
	var serializedTable = <%- JSON.stringify(tableData) %>;
	$(function() {
		$(".data-table-json").DataTable($.extend({}, dataTablesSettings, {
			data: serializedTable,
			order: [[0, "desc"]]
		}));
		$(".section").show();
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Shop Account") %> ID <%= accountDBID %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group">
							<label class="control-label" for="balance"><%= __("Balance") %></label>
							<input type="number" class="form-control boxed" name="balance" value="<%= balance %>" min="0" max="***********">
						</div>
						<div class="form-group">
							<label class="control-label" for="discount"><%= __("Discount") %> (%)</label>
							<input type="number" class="form-control boxed" name="discount" value="<%= discount %>" min="0" max="100">
						</div>
						<div class="form-group">
							<label>
								<input type="checkbox" class="checkbox" name="active" <%- active ? "checked=\"checked\"" : "" %>>
								<span><%= __("Active") %></span>
							</label>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#form").validate(config.validations);
	});
</script>
<%- contentFor("content") %>
<article class="content">
	<div class="title-block">
		<h1 class="title"><%= __("Edit Account") %> ID <%= accountDBID %></h1>
	</div>
	<section class="section">
		<a class="btn btn-secondary history-back" title="" href="#"><i class="fa fa-chevron-left"></i> <%= __("Back to list") %></a>
	</section>
	<section class="section">
		<div class="row">
			<div class="col form">
				<div class="card card-block">
					<form id="form">
						<div class="alert alert-danger" id="errors" style="display: none;"></div>
						<div class="form-group">
							<label class="control-label" for="userName"><%= __("User name") %></label>
							<input type="text" class="form-control boxed" name="userName" value="<%= userName %>" minlength="3" maxlength="24">
						</div>
						<div class="form-group">
						<% if (encryptPasswords) { %>
							<label class="control-label" for="passWord"><%= __("Change password") %></label>
							<input type="text" class="form-control boxed" name="passWord" value="" placeholder="enter a new password to change" minlength="8" maxlength="128">
						<% } else { %>
							<label class="control-label" for="passWord"><%= __("Password") %></label>
							<input type="text" class="form-control boxed" name="passWord" value="<%= passWord %>" minlength="8" maxlength="128">
						<% } %>
						</div>
						<div class="form-group">
							<label class="control-label" for="email"><%= __("Email") %></label>
							<input type="email" class="form-control boxed" name="email" value="<%= email %>">
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label class="control-label" for="permission"><%= __("Permission") %></label>
								<input type="number" class="form-control boxed" name="permission" value="<%= permission %>" min="0" max="10000000000">
							</div>
							<div class="col-sm-6">
								<label class="control-label" for="privilege"><%= __("Privilege") %></label>
								<input type="number" class="form-control boxed" name="privilege" value="<%= privilege %>" min="0" max="10000000000">
							</div>
						</div>
						<div class="form-group row">
							<div class="col-sm-6">
								<label>
									<button type="button" class="btn btn-secondary" id="permission-priority"><i class="fa fa-level-up"></i> <%= __("Set PL") %></button>
								</label>
								<label>
									<button type="button" class="btn btn-secondary" id="permission-maintainer"><i class="fa fa-wrench"></i> <%= __("Set MT") %></button>
								</label>
								<label>
									<button type="button" class="btn btn-secondary" id="permission-reset"><i class="fa fa-refresh"></i> <%= __("Reset") %></button>
								</label>
							</div>
							<div class="col-sm-6">
								<label>
									<button type="button" class="btn btn-secondary" id="privilege-gm"><i class="fa fa-magic"></i> <%= __("Set GM") %></button>
								</label>
								<label>
									<button type="button" class="btn btn-secondary" id="privilege-reset"><i class="fa fa-refresh"></i> <%= __("Reset") %></button>
								</label>
							</div>
						</div>
						<div class="form-group">
							<button type="submit" class="btn btn-primary"><%= __("Save") %></button>
						</div>
					</form>
				</div>
			</div>
		</div>
	</section>
</article>
<%- contentFor("scripts") %>
<script>
	$(function() {
		$("#permission-priority").click(function() {
			$("input[name='permission']").val("1");
		});
		$("#permission-maintainer").click(function() {
			$("input[name='permission']").val("256");
		});
		$("#permission-reset").click(function() {
			$("input[name='permission']").val("0");
		});
		$("#privilege-gm").click(function() {
			$("input[name='privilege']").val("33");
		});
		$("#privilege-reset").click(function() {
			$("input[name='privilege']").val("0");
		});
		$("#form").validate(config.validations);
	});
</script>
<!doctype html>
<html class="no-js" lang="<%= locale %>">
	<head>
		<meta charset="utf-8">
		<meta http-equiv="x-ua-compatible" content="ie=edge">
		<title> TERA API Admin Panel </title>
		<meta name="description" content="">
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<link rel="stylesheet" href="/static/css/vendor.css">
		<link rel="stylesheet" href="/static/css/app.css">
		<link rel="shortcut icon" type="image/x-icon" href="/static/images/favicon.ico" />
	</head>
	<body>
		<div class="auth">
			<div class="auth-container">
				<div class="card">
					<header class="auth-header">
						<h1 class="auth-title">TERA API Admin Panel</h1>
					</header>
					<div class="auth-content">
						<%_ if (errorMessage) { _%>
						<div class="alert alert-danger text-center" id="error-message"><%= errorMessage%></div>
						<%_ } _%>
						<form id="login-form" action="" method="POST" novalidate="">
							<div class="form-group">
								<label for="login"><%= __("Login ID") %></label>
								<input type="text" class="form-control boxed" name="login" id="login" value="" required>
							</div>
							<div class="form-group">
								<label for="password"><%= __("Password") %></label>
								<input type="password" class="form-control boxed" name="password" id="password" value="" required>
							</div>
							<div class="form-group">
								<label for="remember">
									<input class="checkbox" id="remember" name="remember" type="checkbox" <%= login ? "checked" : "" %>>
									<span><%= __("Save account information") %></span>
								</label>
							</div>
							<div class="form-group">
								<button type="submit" class="btn btn-block btn-primary"><%= __("Login") %></button>
							</div>
							<input type="hidden" name="useToken" id="useToken" value="false">
							<input type="hidden" name="tz" id="tz" value="">
						</form>
					</div>
				</div>
			</div>
		</div>
		<script src="/static/js/vendor.js"></script>
		<script src="/static/js/app.js"></script>
		<script>
			$(function() {
				var login = "<%= login %>";
				var password = "<%= password ? `hide_${Date.now()}` : "" %>";

				$("#login").val(login);
				$("#password").val(password);
				$("#useToken").val("<%= login ? "true" : "false" %>");
				$("#tz").val(Intl.DateTimeFormat().resolvedOptions().timeZone);

				var loginValidationSettings = $.extend({}, config.validations, {
					rules: {
						login: "required",
						password: "required"
					},
					messages: {
						login: "<%= __('Please enter login') %>",
						password: "<%= __('Please enter password') %>"
					},
					submitHandler: function(form) {
						form.submit();
					}
				});

				$("#login-form").submit(function() {
					if ($("#login").val() !== login || $("#password").val() !== password) {
						$("#useToken").val("false");
					}
				});

				$("#login-form").validate(loginValidationSettings);
			});
		</script>
	</body>
</html>
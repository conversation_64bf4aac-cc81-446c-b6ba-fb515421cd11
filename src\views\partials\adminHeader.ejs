<%_
const quickMenu = [
	{ page: "/maintenance/add", name: __("Start Maintenance") },
	{ page: "/boxes", name: __("Send Box") },
	{ page: "/benefits/add", name: __("Add Benefit") },
	{ page: "/bans/add", name: __("Ban Account") },
	{ page: "/shop_products/add", name: __("Add Product") },
	{ page: "/shop_categories/add", name: __("Add Category") }
];
_%>
<header class="header">
	<div class="header-block header-block-collapse d-lg-none d-xl-none">
		<button class="collapse-btn" id="sidebar-collapse-btn">
			<i class="fa fa-bars"></i>
		</button>
	</div>
	<div class="header-block header-block-search">
		<form role="search">
			<div class="input-container">
				<select id="quick-action" class="form-control underlined">
					<option><%= __("Quick Actions") %>...</option>
				<%_ quickMenu.forEach(item => { _%>
					<%_ if (user.type !== "steer" || Object.values(user.functions).includes(item.page)) { _%>
					<option value="<%= item.page %>"><%= item.name %></option>
					<%_ } _%>
				<%_ }) _%>
				</select>
				<div class="underline"></div>
			</div>
		</form>
	</div>
	<div class="header-block header-block-buttons">
	<%_ __quickMenu.forEach(entry => { _%>
		<a href="<%= entry.url %>" class="btn btn-sm header-btn" target="_blank"><i class="fa fa-link"></i> <span><%= entry.name %></span></a>
	<%_ }) _%>
	</div>
	<div class="header-block header-block-nav">
		<ul class="nav-profile">
			<li class="notifications new" id="notifications">
				<a href="" data-toggle="dropdown" id="notifications-dropdown">
					<i class="fa fa-bell-o"></i>
					<sup><span class="counter" id="counter">0</span></sup>
				</a>
				<div class="dropdown-menu notifications-dropdown-menu">
					<ul class="notifications-container" id="notifications-container" style="display: none;"></ul>
					<footer>
						<ul>
							<li><a class="text-primary pt-1 pb-1" href="/tasks"><%= __("Show all tasks") %></a></li>
						</ul>
					</footer>
				</div>
			</li>
			<li class="profile dropdown">
				<a class="nav-link dropdown-toggle" data-toggle="dropdown" href="#" role="button" aria-haspopup="true" aria-expanded="false">
					<span class="name"><%= user.login %></span>
				</a>
				<div class="dropdown-menu profile-dropdown-menu" aria-labelledby="dropdownMenu1">
					<a class="dropdown-item" href="/profile"><i class="fa fa-user icon"></i> <%= __("Profile") %></a>
					<a class="dropdown-item" href="/logout"><i class="fa fa-power-off icon"></i> <%= __("Logout") %></a>
				</div>
			</li>
		</ul>
	</div>
</header>